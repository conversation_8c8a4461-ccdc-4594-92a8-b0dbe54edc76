<?php

namespace App\Common;

class Constant
{
    // HTTP status code
    const HTTP_STATUS_OK                    = 200;
    const HTTP_STATUS_CREATED               = 201;
    const HTTP_STATUS_BAD_REQUEST           = 400;
    const HTTP_STATUS_UNAUTHORIZED          = 401;
    const HTTP_STATUS_FORBIDDEN             = 403;
    const HTTP_STATUS_NOT_FOUND             = 404;
    const HTTP_STATUS_NOT_ACCEPTABLE        = 406;
    const HTTP_STATUS_UNPROCESSABLE_ENTITY  = 422;

    // Methods
    const PUT_METHOD = 'PUT';

    // Pagination
    const DEFAULT_LIMIT        = 10;
    const DEFAULT_DRAW         = 1;
    const DEFAULT_OFFSET       = 0;
    const DEFAULT_DIRECTION    = 'asc';
    const DEFAULT_ORDER_COLUMN = 1;
    const DEFAULT_COLUMN       = 'id';
    const PER_PAGE_VALUES      = [25, 50, 100];
    const SORT_DESCENDING      = 'desc';
    const DEFAULT_PER_PAGE     = 25;
    const LIMIT_100_PER_PAGE   = 100;
    const DEFAULT_PAGE         = 1;
    const DEFAULT_PAGE_NAME    = 'page';

    // Format date
    const DATE_FORMAT_MYSQL      = 'Y-m-d';
    const TIME_FORMAT_MYSQL      = 'H:i:s';
    const DATETIME_FORMAT_MYSQL  = 'Y-m-d H:i:s';
    const START_TIME_A_DAY       = '00:00:00';
    const END_TIME_A_DAY         = '23:59:59';
    const DEFAULT_DATETIME_MYSQL = '0000-00-00 00:00:00';

    // Genders
    const GENDERS = [0 => 'male', 1 => 'female', 2 => 'no_answer', 3 => 'other'];

    // Statuses
    const ACTIVE        = 1;
    const INACTIVE      = 0;
    const DELETED       = 2;
    const STATUS_VALUES = [self::ACTIVE, self::INACTIVE];

    // Yes no status
    const YES_NO_VALUES = [0 => 'no', 1 => 'yes'];

    // Validation
    const VALIDATION_RULE = [
        'minLength8'          => 8,
        'maxLength32'         => 32,
        'maxLength50'         => 50,
        'maxLength64'         => 64,
        'maxLength255'        => 255,
        'regexDecimal2Places' => '/^\d+(\.\d{1,2})?$/',
        'dateFormat'          => self::DATE_FORMAT_MYSQL
    ];

    // Language
    const DEFAULT_LANGUAGE        = 'vn';
    const LANGUAGE_VALUES         = ['vn', 'en'];

    // Migration
    const LIMIT_IMAGE_NAME           = 128;
    const LIMIT_4000_CHARACTERS      = 4000;
    const LIMIT_1000_CHARACTERS      = 1000;
    const LIMIT_256_CHARACTERS       = 256;
    const LIMIT_255_CHARACTERS       = 255;
    const LIMIT_150_CHARACTERS       = 150;
    const LIMIT_64_CHARACTERS        = 64;
    const LIMIT_50_CHARACTERS        = 50;
    const LIMIT_32_CHARACTERS        = 32;
    const LIMIT_20_CHARACTERS        = 19;
    const LIMIT_19_CHARACTERS        = 19;
    const LIMIT_11_CHARACTERS        = 11;
    const LIMIT_10_CHARACTERS        = 10;
    const LIMIT_7_CHARACTERS         = 7;
    const DEFAULT_ZERO_VALUE         = 0;
    const DEFAULT_BUSINESS_TIME_FROM = '00:00:00';
    const DEFAULT_BUSINESS_TIME_TO   = '23:45:00';
    const DEFAULT_TIME_FROM_SEED     = '08:00:00';
    const DEFAULT_TIME_TO_SEED       = '23:00:00';
    const DEFAULT_STATUSES           = [0 => 'inactive', 1 => 'active', 2 => 'delete'];

    // ADS Common
    const DEFAULT_ADS_STATUS           = 0;
    const DEFAULT_ADS_INACTIVE_STATUS  = 0;
    const DEFAULT_ADS_ACTIVE_STATUS    = 1;
    const DEFAULT_ADS_DELETED_STATUS   = 2;
    const DEFAULT_ADS_WAITING_APPROVED = 3;
    const DEFAULT_ADS_APPROVED         = 4;
    const DEFAULT_ADS_DRAFT_STATUS     = 5;
    const DEFAULT_ADS_PAUSED_STATUS    = 6;
    const ADS_STATUSES                 = [
        self::DEFAULT_ADS_ACTIVE_STATUS   => 'active',
        self::DEFAULT_ADS_INACTIVE_STATUS => 'inactive',
        self::DEFAULT_ADS_DELETED_STATUS  => 'deleted'
    ];
    const ADS_APPROVED_STATUSES                = [
        self::DEFAULT_ADS_ACTIVE_STATUS   => 'active',
        self::DEFAULT_ADS_INACTIVE_STATUS => 'inactive',
        self::DEFAULT_ADS_DELETED_STATUS  => 'deleted',
        self::DEFAULT_ADS_WAITING_APPROVED  => 'waiting_approved',
        self::DEFAULT_ADS_APPROVED  => 'approved',
    ];
    const ADS_TOTAL_DECIMAL_PROGRESS = 5;
    const ADS_RANDOM_ID_LENGTH       = 6;


    // ADS Client
    const DEFAULT_ADS_CLIENT_TYPE   = 0;
    const ADS_CLIENT_TYPES          = [0 => 'white_label', 1 => 'solutions'];
    const ADS_CLIENT_CLIENT_ID_LENGTH = 6;

    // ADS Advertiser
    const DEFAULT_ADS_ADVERTISER_TYPE = 0;
    const ADS_ADVERTISER_TYPES        = [0 => 'brand', 1 => 'publisher', 2 => 'agency'];

    // [REPORT_ADVERTISERS, REPORT_CAMPAIGNS] STATUS
    const REPORT_STATUS_DEFAULT = 1;
    const REPORT_STATUS_ACTIVE = 1;
    const REPORT_STATUS_INUSE = 2;
    const REPORT_STATUS_DELETED = 0;
    const REPORT_STATUSES                = [
        self::REPORT_STATUS_ACTIVE => 'Active',
        self::REPORT_STATUS_INUSE   => 'Inactive',
        self::REPORT_STATUS_DELETED  => 'Deleted'
    ];


    // source channel report
    const CHANNEL_REPORT_DV360 = 'DV360';
    const CHANNEL_REPORT_FACEBOOK = 'FB';
    const CHANNEL_REPORT_TIKTOK = 'TIKTOK';
    const CHANNEL_REPORT_OTT = 'OTT';
    const CHANNEL_REPORT_GOOGLE_ADS = 'GOOGLEADS';
    const CHANNEL_REPORT_ZALO = 'ZALO';
    const CHANNEL_REPORT_APPLE_SEARCH = 'APPLESEARCH';
    const CHANNEL_REPORT_GA = 'GA';
    const CHANNEL_REPORT_LINKEDIN = 'LINKEDIN';
    const CHANNEL_REPORT_OTHER = 'OTHER';
    const REPORT_CHANNELS                = [
        self::CHANNEL_REPORT_DV360 => 'Google DV360',
        self::CHANNEL_REPORT_FACEBOOK   => 'Facebook',
        self::CHANNEL_REPORT_TIKTOK   => 'Tiktok',
        self::CHANNEL_REPORT_OTT => 'OTT',
        self::CHANNEL_REPORT_GOOGLE_ADS => 'Google Ads',
        self::CHANNEL_REPORT_ZALO => 'Zalo ads',
        self::CHANNEL_REPORT_APPLE_SEARCH => 'Apple Search',
        self::CHANNEL_REPORT_GA => 'Google Analytics',
        self::CHANNEL_REPORT_LINKEDIN => 'Linkedin',
        self::CHANNEL_REPORT_OTHER => 'Other'
    ];

    // buy_type map plan report
    const BUY_TYPE_PLAN_CPM = 'CPM';
    const BUY_TYPE_PLAN_CPC = 'CPC';
    const BUY_TYPE_PLAN_CPCV = 'CPCV';
    const BUY_TYPES = [
        self::BUY_TYPE_PLAN_CPM => "impressions",
        self::BUY_TYPE_PLAN_CPC => "clicks",
        self::BUY_TYPE_PLAN_CPCV => "completed_view"
    ];
    const DEFAULT_METRIC_BUYTYPE = 'impressions';
    const ER_METRIC_BUYTYPE = 'er';
    const CPM_METRIC_BUYTYPE = 'cpm';
    const CPV_METRIC_BUYTYPE = 'cpv';
    const CPC_METRIC_BUYTYPE = 'cpc';
    const CPE_METRIC_BUYTYPE = 'cpe';
    const CPPL_METRIC_BUYTYPE = 'cppl';
    const PAGELIKE_METRIC_BUYTYPE = 'plr';
    const VIEWRATE_METRIC_BUYTYPE = 'vtr';
    const CLICKS_METRIC_BUYTYPE = 'ctr';
    const LINK_CLICKS_METRIC_BUYTYPE = 'lctr';
    const CONVERSION_RATE_METRIC_BUYTYPE = 'cr';

    // [CUSTOM_REPORT_OPTIONS_TYPE] STATUS
    const CUSTOM_REPORT_TYPE_DISPLAY = 1;
    const CUSTOM_REPORT_TYPE_CALC = 2;
    const CUSTOM_REPORT_TYPE_CURRENCY = 3;
    const CUSTOM_REPORT_TYPE_DEMOGRAPHIC = 4;
    const CUSTOM_REPORT_TYPE_SHOW_PLAN_MONITOR = 5;

    const CUSTOM_REPORT_TYPE = [
        self::CUSTOM_REPORT_TYPE_DISPLAY => "display",
        self::CUSTOM_REPORT_TYPE_CALC => "calculation",
        self::CUSTOM_REPORT_TYPE_CURRENCY => "currency",
        self::CUSTOM_REPORT_TYPE_DEMOGRAPHIC => "demographic",
        self::CUSTOM_REPORT_TYPE_SHOW_PLAN_MONITOR => "plan_monitor"
    ];

    const CUSTOM_OPTION_SWITCH_ON = 'on';
    const CUSTOM_OPTION_SWITCH_OFF = 'off';


    const REPORT_INSIGHT_AGE = 'age';
    const REPORT_INSIGHT_DEVICE = 'device';
    const REPORT_INSIGHT_GENDER = 'gender';
    const REPORT_INSIGHT_LOCATION = 'location';
    const REPORT_INSIGHT_REGION = 'region';
    const REPORT_INSIGHT_COUNTRY = 'country';
    const REPORT_INSIGHT_CITY = 'city';
    const INSIGHT_REPORT_AGE = [
        "18-24",
        "25-34",
        "35-44",
        "45-54",
        "55-64",
        "65+"
    ];

    const YOUTUBE = 'youtube';
    const REPORT_INSIGHTS_FIELD = [
        self::CHANNEL_REPORT_DV360 => [
            self::REPORT_INSIGHT_AGE => self::REPORT_INSIGHT_AGE,
            self::REPORT_INSIGHT_DEVICE => self::REPORT_INSIGHT_DEVICE,
            self::REPORT_INSIGHT_GENDER => self::REPORT_INSIGHT_GENDER,
        ],
        self::CHANNEL_REPORT_FACEBOOK => [
            self::REPORT_INSIGHT_AGE => self::REPORT_INSIGHT_AGE,
            self::REPORT_INSIGHT_GENDER => self::REPORT_INSIGHT_FB_GENDER,
        ]
    ];

    const DATA_SOURCE_API = 'api';
    const DATA_SOURCE_MANUAL = 'manual';

    const DATA_SOURCES = [
        self::DATA_SOURCE_API => 'API',
        self::DATA_SOURCE_MANUAL => 'Manual',
    ];

    const DEFAULT_ADVERTISER_FACTOR_MEDIA_COST = 1;
    const DEFAULT_PLAN_FACTOR_MEDIA_COST = 2;
    const DEFAULT_OPERATION_MEDIA_COST = 'mul';
    const DIV_OPERATION_MEDIA_COST = 'div';
    const PLUS_OPERATION_MEDIA_COST = 'plus';
    const MINUS_OPERATION_MEDIA_COST = 'minus';
    const DEFAULT_CURRENCY = 'usd';
    const DEFAULT_EXCHANGE_RATE = '23500';
    const FLAG_EXCEED_VOL_KPI = 1;
    const FLAG_NOT_EXCEED_VOL_KPI = 0;

    // Image folders
    const LOGO_FOLDER = 'images/clients';
    const DEFAULT_NO_IMAGE = 'template/main/assets/images/no-image.jpg';
    const PUBLIC_IMAGE_TEMPLATE = 'template/main/assets';

    const MAPPING_CREATIVE_GROUP = 'creative_group';
    const MAPPING_CREATIVE = 'creative';
    const MAPPINGCREATIVE = [
        self::MAPPING_CREATIVE_GROUP => "Creative Group",
        self::MAPPING_CREATIVE => "Creative",
    ];

    // Storage
    const DEFAULT_STORAGE_PATH = 'storage';

    //permission
    const ADMIN = 'NETWORK_ADMIN';
    const STANDARD_USER = 'STANDARD';
    const MEDIA_SENIOR_USER = "MEDIA-SENIOR";
    const CLIENT_ADMIN_USER = "CLIENT-ADMIN";
    const CLIEN_BOD_USER = "CLIEN-BOD";
    const CREATIVE_MAKER_USER = "CREATIVE-MAKER";
    const REPORT_USER = "REPORT";
    const BRAND_VIEWER_USER = "BRAND-VIEWER";
    const ADMIN_USER = "ADMIN";
    const CLIENT_EXTERNAL_USER = "CLIENT-EXTERNAL";
    const ADVANCED_USER = "ADVANCED";
    const REPORTING_USER = "REPORTING";
    const CREATIVE_USER = "CREATIVE";

    const MANUAL_UPLOAD_DATA_SOURCE_NEW = 0;
    const MANUAL_UPLOAD_DATA_SOURCE_PROCESSING = 1;
    const MANUAL_UPLOAD_DATA_SOURCE_SUCCESS = 2;
    const MANUAL_UPLOAD_DATA_SOURCE_ERROR = 3;
    const MANUAL_UPLOAD_DATA_STATUS = [
        self::MANUAL_UPLOAD_DATA_SOURCE_NEW => self::MANUAL_UPLOAD_DATA_SOURCE_NEW,
        self::MANUAL_UPLOAD_DATA_SOURCE_PROCESSING => self::MANUAL_UPLOAD_DATA_SOURCE_PROCESSING,
        self::MANUAL_UPLOAD_DATA_SOURCE_SUCCESS => self::MANUAL_UPLOAD_DATA_SOURCE_SUCCESS,
        self::MANUAL_UPLOAD_DATA_SOURCE_ERROR => self::MANUAL_UPLOAD_DATA_SOURCE_ERROR,
    ];

    const DATE_SORT = 'date';

    //plan_id kpi  VTR = view/impression
    const KPI_NAME_VIEW = [
        "trueview",
        "trueview_views",
        "video_views",
        "video_thruplay_watched_actions",
        "rich_media_video_completions",
        "video_view", // video view 3s
        "video_continuous_2_sec_watched_actions",
        "video_views",
        "video_play_actions",
        "video_watched_2s",
        "video_watched_6s",
        "video_views_p100",
        "video_views_at_25",
    ];

    const PLATFORM_KEY_ALL_PLATFORM = 'all_platform';
    const PLATFORM_KEY_ANDROID = 'android';
    const PLATFORM_KEY_IOS = 'ios';
    const PLATFORM_KEY_CHANNEL = 'channel';
    const PLATFORM_KEY_CONNECTED_TV = 'connected_tv';
    const PLATFORMS_DATA = [
        self::PLATFORM_KEY_ALL_PLATFORM => 'All platforms',
        self::PLATFORM_KEY_ANDROID      => 'Android',
        self::PLATFORM_KEY_IOS          => 'iOS',
        self::PLATFORM_KEY_CONNECTED_TV => 'Connected TV',
        self::PLATFORM_KEY_CHANNEL      => 'Channel',
    ];

    const PLATFORM_CATEGORY_URL_KEY = 'URL';
    const PLATFORM_CATEGORY_UNKNOWN_KEY = 'UNKNOWN';
    const PLATFORM_CATEGORY_CHANNEL_KEY = 'CHANNEL';
    const PLATFORM_CATEGORY_IOS_KEY = 'APP_PLATFORM_IOS';
    const PLATFORM_CATEGORY_ANDROID_KEY = 'APP_PLATFORM_ANDROID';
    const PLATFORM_CATEGORY_ANDROID_TV_KEY = 'APP_PLATFORM_ANDROID_TV';
    const PLATFORM_CATEGORY_SAMSUNG_TV_KEY = 'APP_PLATFORM_SAMSUNG_TV';
    const PLATFORM_CATEGORY_AMAZON_FIRETV_KEY = 'APP_PLATFORM_AMAZON_FIRETV';
    const PLATFORM_CATEGORY_APPLE_TV_KEY = 'APP_PLATFORM_APPLE_TV';
    const PLATFORM_CATEGORY_ROKU_KEY = 'APP_PLATFORM_ROKU';
    const PLATFORM_CATEGORIES_DATA = [
        self::PLATFORM_CATEGORY_URL_KEY           => 'URL',
        self::PLATFORM_CATEGORY_UNKNOWN_KEY       => 'UNKNOWN',
        self::PLATFORM_CATEGORY_CHANNEL_KEY       => 'CHANNEL',
        self::PLATFORM_CATEGORY_IOS_KEY           => 'APP_PLATFORM_IOS',
        self::PLATFORM_CATEGORY_ANDROID_KEY       => 'APP_PLATFORM_ANDROID',
        self::PLATFORM_CATEGORY_ANDROID_TV_KEY    => 'APP_PLATFORM_ANDROID_TV',
        self::PLATFORM_CATEGORY_SAMSUNG_TV_KEY    => 'APP_PLATFORM_SAMSUNG_TV',
        self::PLATFORM_CATEGORY_AMAZON_FIRETV_KEY => 'APP_PLATFORM_AMAZON_FIRETV',
        self::PLATFORM_CATEGORY_APPLE_TV_KEY      => 'APP_PLATFORM_APPLE_TV',
        self::PLATFORM_CATEGORY_ROKU_KEY          => 'APP_PLATFORM_ROKU',
    ];


    const EXPORT_CSV_OVERALL_PERFORMANCE = "selection_export_customer_charts";
    const EXPORT_CSV_INSIGHT_AGE = "selection_export_age_charts";
    const EXPORT_CSV_INSIGHT_GENDER = "selection_export_gender_charts";
    const EXPORT_CSV_INSIGHT_DEVICE = "selection_export_device_charts";
    const EXPORT_CSV_INSIGHT_REGION = "selection_export_region_charts";
    const EXPORT_CSV_TOP_CREATIVE_GROUP = "selection-export-campaign-top-creative-group";
    const EXPORT_CSV_YOUTUBE_ADGROUP = "selection-export-performance-by-youtube-adgroup";
    const EXPORT_CSV_APP_URL = "selection-export-performance-by-app-url";
    const EXPORT_CSV_CREATIVE_GROUP_PERFORMANCE = "selection-export-creative-group-performance";

    const EXPORT_CSV_TRACKING_PLAN = [
        self::EXPORT_CSV_OVERALL_PERFORMANCE,
        self::EXPORT_CSV_INSIGHT_AGE,
        self::EXPORT_CSV_INSIGHT_GENDER,
        self::EXPORT_CSV_INSIGHT_DEVICE,
        self::EXPORT_CSV_INSIGHT_REGION,
        self::EXPORT_CSV_TOP_CREATIVE_GROUP,
        self::EXPORT_CSV_YOUTUBE_ADGROUP,
        self::EXPORT_CSV_APP_URL,
        self::EXPORT_CSV_CREATIVE_GROUP_PERFORMANCE,
    ];

    const EXPORT_CSV_TRACKING_PLAN_HEADER = [
        self::EXPORT_CSV_OVERALL_PERFORMANCE => 'Overall Performance break by date',
        self::EXPORT_CSV_INSIGHT_AGE => 'Age Insight',
        self::EXPORT_CSV_INSIGHT_GENDER => 'Gender Insight',
        self::EXPORT_CSV_INSIGHT_DEVICE => 'Device Insight',
        self::EXPORT_CSV_INSIGHT_REGION => 'Region Insight',
        self::EXPORT_CSV_TOP_CREATIVE_GROUP => 'Top TA',
        self::EXPORT_CSV_YOUTUBE_ADGROUP => 'Performance By Youtube Ad group',
        self::EXPORT_CSV_APP_URL => 'Performance By App/URL',
        self::EXPORT_CSV_CREATIVE_GROUP_PERFORMANCE => 'Creative Group Performance',
    ];

    const MICRO_NUMBER = 1000000;

    const REPORT_DEVICE_RENAME = [
        'Phone' => 'Mobile',
        'Pc'    => 'PC',
    ];

    //defined type of image from ad creative facebook
    const FB_CREATIVE_IMAGE_TYPE_IMAGE_URL         = 'IMAGE_URL';
    const FB_CREATIVE_IMAGE_TYPE_VIDEO_IMAGE_URL   = 'VIDEO_IMAGE_URL';
    const FB_CREATIVE_IMAGE_TYPE_THUMBNAIL_URL     = 'THUMBNAIL_URL';

    const DIFFER_ONEDAY_TIME_PLAN_TIMING = 86400;
    const CHANNEL_REPORT_REALTIME = [
        self::CHANNEL_REPORT_FACEBOOK,
        self::CHANNEL_REPORT_DV360
    ];

    const GOOGLE_ADS_ASSETS_YTB_VIDEO = "YOUTUBE_VIDEO";
    const GOOGLE_ADS_ASSETS_IMAGE = "IMAGE";

    const GOOGLE_ADS_ASSETS_SUPPORT = [
        self::GOOGLE_ADS_ASSETS_YTB_VIDEO,
        self::GOOGLE_ADS_ASSETS_IMAGE,
    ];

    const ECOM_LAZADA_ECOMMERCE_PLATFORM_TYPE = 'lazada';
    const ECOM_SHOPEE_ECOMMERCE_PLATFORM_TYPE = 'shopee';
    const ECOM_TIKI_ECOMMERCE_PLATFORM_TYPE = 'tiki';

    const ECOM_TIKTOK_PLATFORM_TYPE = 'tiktok';
    const ECOM_PLATFORM_TYPE = 'ecom';
}
