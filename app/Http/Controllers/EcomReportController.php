<?php

namespace App\Http\Controllers;

use App\Common\Constant;
use App\Models\EcomReport;
use App\Services\EcomReportCellConfigService;
use Google\Client;
use Google\Service\Sheets;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;

class EcomReportController extends Controller
{
    /**
     * Display the e-commerce report.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request, EcomReport $report): View
    {
        $reports = EcomReport::all();

        return view('reports.ecom.index', ['reports' => $reports]);
    }

    public function config(Request $request, EcomReport $report): View
    {
        $reportType = $request->query('report_type');

        // Handle different report types
        switch ($reportType) {
            case 'overall':
                return $this->showOverallReport($request, $report);
            case 'overall-lazada':
                return $this->showOverallLazadaReport($request, $report);
            case 'overall-shopee':
                return $this->showOverallShopeeReport($request, $report);
            case 'overall-shopee-sem-pmax':
                return $this->showOverallShopeeSemPmaxReport($request, $report);
            case 'overall-tiki':
                return $this->showOverallTikiReport($request, $report);
            case 'lazada-offsite-fb-conversion':
                return $this->showLazadaOffsiteFbConversionReport($request, $report);
            case 'lazada-offsite-fb-cpas':
                return $this->showLazadaOffsiteFbCpasReport($request, $report);
            case 'lazada-onsite-search-discovery':
                return $this->showLazadaOnsiteSearchDiscoveryReport($request, $report);
            case 'lazada-onsite-affiliate':
                return $this->showLazadaOnsiteAffiliateReport($request, $report);
            case 'lazada-organic-untrackable':
                return $this->showLazadaOrganicUntrackableReport($request, $report);
            case 'shopee-offsite-google-sem':
                return $this->showShopeeOffsiteGoogleSemReport($request, $report);
            case 'shopee-offsite-fb-conversion':
                return $this->showShopeeOffsiteFbConversionReport($request, $report);
            case 'shopee-offsite-fb-cpas':
                return $this->showShopeeOffsiteFbCpasReport($request, $report);
            case 'shopee-onsite-search-discovery':
                return $this->showShopeeOnsiteSearchDiscoveryReport($request, $report);
            case 'shopee-onsite-affiliate':
                return $this->showShopeeOnsiteAffiliateReport($request, $report);
            case 'shopee-organic-untrackable':
                return $this->showShopeeOrganicUntrackableReport($request, $report);
            case 'tiki-onsite-search-discovery':
                return $this->showTikiOnsiteSearchDiscoveryReport($request, $report);
            case 'tiki-organic-untrackable':
                return $this->showTikiOrganicUntrackableReport($request, $report);
            case 'shopee-offsite-youtube-trueview-action':
                return $this->showShopeeOffsiteYoutubeTrueviewActionReport($request, $report);
            default:
                // If report type not found, redirect to main index
                return $this->showOverallReport($request, $report);
        }
    }

    /**
     * Display the e-commerce report view.
     *
     * @return \Illuminate\View\View
     */
    public function view(Request $request, EcomReport $report): View
    {
        $reportType = $request->query('report_type');
        $reportId = $request->query('report_id');
        if (!empty($reportId)) {
            $report = EcomReport::find($reportId);
        }

        // If no report type specified, show the main index
        if (!$reportType) {
            $reports = EcomReport::all();
            return view('reports.ecom.index', ['reports' => $reports]);
        }

        $view = true;

        // Handle different report types
        switch ($reportType) {
            case 'overall':
                return $this->showOverallReport($request, $report, $view);
            case 'overall-lazada':
                return $this->showOverallLazadaReport($request, $report, $view);
            case 'overall-shopee':
                return $this->showOverallShopeeReport($request, $report, $view);
            case 'overall-shopee-sem-pmax':
                return $this->showOverallShopeeSemPmaxReport($request, $report, $view);
            case 'overall-tiki':
                return $this->showOverallTikiReport($request, $report, $view);
            case 'lazada-offsite-fb-conversion':
                return $this->showLazadaOffsiteFbConversionReport($request, $report, $view);
            case 'lazada-offsite-fb-cpas':
                return $this->showLazadaOffsiteFbCpasReport($request, $report, $view);
            case 'lazada-onsite-search-discovery':
                return $this->showLazadaOnsiteSearchDiscoveryReport($request, $report, $view);
            case 'lazada-onsite-affiliate':
                return $this->showLazadaOnsiteAffiliateReport($request, $report, $view);
            case 'lazada-organic-untrackable':
                return $this->showLazadaOrganicUntrackableReport($request, $report, $view);
            case 'shopee-offsite-google-sem':
                return $this->showShopeeOffsiteGoogleSemReport($request, $report, $view);
            case 'shopee-offsite-fb-conversion':
                return $this->showShopeeOffsiteFbConversionReport($request, $report, $view);
            case 'shopee-offsite-fb-cpas':
                return $this->showShopeeOffsiteFbCpasReport($request, $report, $view);
            case 'shopee-onsite-search-discovery':
                return $this->showShopeeOnsiteSearchDiscoveryReport($request, $report, $view);
            case 'shopee-onsite-affiliate':
                return $this->showShopeeOnsiteAffiliateReport($request, $report, $view);
            case 'shopee-organic-untrackable':
                return $this->showShopeeOrganicUntrackableReport($request, $report, $view);
            case 'tiki-onsite-search-discovery':
                return $this->showTikiOnsiteSearchDiscoveryReport($request, $report, $view);
            case 'tiki-organic-untrackable':
                return $this->showTikiOrganicUntrackableReport($request, $report, $view);
            case 'shopee-offsite-youtube-trueview-action':
                return $this->showShopeeOffsiteYoutubeTrueviewActionReport($request, $report, $view);
            default:
                // If report type not found, redirect to main index
                return $this->showOverallReport($request, $report, $view);
        }
    }

    /**
     * Show overall report
     */
    private function showOverallReport(Request $request, EcomReport $report, bool $view = false): View
    {
        $title = "CUCKOO - OVERALL - ECOM PLAN";
        // For now, return the existing edit view - you can customize this later
        return view('reports.ecom.overall', ['reportType' => 'overall', 'report' => $report, 'title' => $title, 'view' => $view]);
    }

    /**
     * Show overall Lazada report
     */
    private function showOverallLazadaReport(Request $request, EcomReport $report, bool $view = false): View
    {
        // Get the first report for now - you might want to make this configurable
        return view('reports.ecom.overall-lazada', compact('report', 'view'));
    }

    /**
     * Show overall Shopee report
     */
    private function showOverallShopeeReport(Request $request, EcomReport $report, bool $view = false): View
    {
        return view('reports.ecom.overall-shopee', compact('report', 'view'));
    }

    /**
     * Show overall Shopee SEM & PMAX report
     */
    private function showOverallShopeeSemPmaxReport(Request $request, EcomReport $report, bool $view = false): View
    {
        return view('reports.ecom.overall-shopee-sem-pmax', compact('report', 'view'));
    }

    /**
     * Show overall Tiki report
     */
    private function showOverallTikiReport(Request $request, EcomReport $report, bool $view = false): View
    {
        return view('reports.ecom.overall-tiki', compact('report', 'view'));
    }

    /**
     * Show Lazada offsite Facebook conversion report
     */
    private function showLazadaOffsiteFbConversionReport(Request $request, EcomReport $report, bool $view = false): View
    {
        return view('reports.ecom.lazada-offsite-fb-conversion', compact('report', 'view'));
    }

    /**
     * Show Lazada offsite Facebook CPAS report
     */
    private function showLazadaOffsiteFbCpasReport(Request $request, EcomReport $report, bool $view = false): View
    {
        return view('reports.ecom.lazada-offsite-fb-cpas', compact('report', 'view'));
    }

    /**
     * Show Lazada onsite search discovery report
     */
    private function showLazadaOnsiteSearchDiscoveryReport(Request $request, EcomReport $report, bool $view = false): View
    {
        return view('reports.ecom.lazada-onsite-search-discovery', compact('report', 'view'));
    }

    /**
     * Show Lazada onsite affiliate report
     */
    private function showLazadaOnsiteAffiliateReport(Request $request, EcomReport $report, bool $view = false): View
    {
        return view('reports.ecom.lazada-onsite-affiliate', compact('report', 'view'));
    }

    /**
     * Show Lazada organic untrackable report
     */
    private function showLazadaOrganicUntrackableReport(Request $request, EcomReport $report, bool $view = false): View
    {
        return view('reports.ecom.lazada-organic-untrackable', compact('report', 'view'));
    }

    /**
     * Show Shopee offsite Google SEM report
     */
    private function showShopeeOffsiteGoogleSemReport(Request $request, EcomReport $report, bool $view = false): View
    {
        return view('reports.ecom.shopee-offsite-google-sem', compact('report', 'view'));
    }

    /**
     * Show Shopee offsite Facebook conversion report
     */
    private function showShopeeOffsiteFbConversionReport(Request $request, EcomReport $report, bool $view = false): View
    {
        return view('reports.ecom.shopee-offsite-fb-conversion', compact('report', 'view'));
    }

    /**
     * Show Shopee offsite Facebook CPAS report
     */
    private function showShopeeOffsiteFbCpasReport(Request $request, EcomReport $report, bool $view = false): View
    {
        return view('reports.ecom.shopee-offsite-fb-cpas', compact('report', 'view'));
    }

    /**
     * Show Shopee offsite Youtube Trueview Action report
     */
    private function showShopeeOffsiteYoutubeTrueviewActionReport(Request $request, EcomReport $report, bool $view = false): View
    {
        return view('reports.ecom.shopee-offsite-youtube-trueview-action', compact('report', 'view'));
    }

    /**
     * Show Shopee onsite search discovery report
     */
    private function showShopeeOnsiteSearchDiscoveryReport(Request $request, EcomReport $report, bool $view = false): View
    {
        return view('reports.ecom.shopee-onsite-search-discovery', compact('report', 'view'));
    }

    /**
     * Show Shopee onsite affiliate report
     */
    private function showShopeeOnsiteAffiliateReport(Request $request, EcomReport $report, bool $view = false): View
    {
        return view('reports.ecom.shopee-onsite-affiliate', compact('report', 'view'));
    }

    /**
     * Show Shopee organic untrackable report
     */
    private function showShopeeOrganicUntrackableReport(Request $request, EcomReport $report, bool $view = false): View
    {
        return view('reports.ecom.shopee-organic-untrackable', compact('report', 'view'));
    }

    /**
     * Show Tiki onsite search discovery report
     */
    private function showTikiOnsiteSearchDiscoveryReport(Request $request, EcomReport $report, bool $view = false): View
    {
        return view('reports.ecom.tiki-onsite-search-discovery', compact('report', 'view'));
    }

    /**
     * Show Tiki organic untrackable report
     */
    private function showTikiOrganicUntrackableReport(Request $request, EcomReport $report, bool $view = false): View
    {
        return view('reports.ecom.tiki-organic-untrackable', compact('report', 'view'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'sheet_id' => 'required|string|max:255',
        ]);

        // Create a new EcomReport instance
        $ecomReport = new EcomReport();

        // Assign the validated data to the model's attributes
        $ecomReport->name = $validatedData['name'];
        $ecomReport->sheet_id = $validatedData['sheet_id'];

        // You might want to add user ID if you track who created the report
        $ecomReport->created_by = auth()->id() ?? Constant::DEFAULT_ZERO_VALUE;
        $ecomReport->updated_by = auth()->id() ?? Constant::DEFAULT_ZERO_VALUE;

        // Save the new EcomReport record to the database
        $ecomReport->save();

        // Redirect back with a success message
        return redirect()->route('ecom-reports.index') // Assuming you have an index route to list ECOM reports
            ->with('success', 'ECOM Report created successfully.');
    }

    /**
     * Display the e-commerce report edit.
     *
     * @return \Illuminate\View\View
     */
    public function edit(EcomReport $report): View
    {
        return view('reports.ecom.edit', compact('report'));
    }

    /**
     * Update the specified e-commerce report.
     *
     * @param Request $request
     * @param EcomReport $report
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, EcomReport $report)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'sheet_id' => 'required|string|max:255',
        ]);

        try {
            $report->name = $validatedData['name'];
            $report->sheet_id = $validatedData['sheet_id'];
            $report->updated_by = auth()->id() ?? Constant::DEFAULT_ZERO_VALUE;
            $report->save();

            return response()->json([
                'success' => true,
                'message' => 'ECOM Report updated successfully.',
                'report' => $report
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update ECOM Report: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Title
     *
     * @param mixed
     * @param EcomReport $report
     * @return \Illuminate\Http\JsonResponse
     */
    public function getReportData(Request $request, EcomReport $report, EcomReportCellConfigService $cellConfigService)
    {
        // Only accept reportId parameter to ensure correct report settings are used
        $reportId = $report->id;

        if (!$reportId) {
            return response()->json(['error' => 'reportId parameter is required'], 400);
        }

        $tableId = $request->query('tableId') ?? $request->query('table_id');

        $startDateString = $request->query('start_date');
        $endDateString = $request->query('end_date');

        // Validate or set default dates
        try {
            $startDate = $startDateString ? Carbon::parse($startDateString)->startOfDay() : null;
            $endDate = $endDateString ? Carbon::parse($endDateString)->endOfDay() : null;
        } catch (\Exception $e) {
            return response()->json(['error' => 'Invalid date format. Please use YYYY-MM-DD.'], 400);
        }

        // Find the report by reportId
        $report = EcomReport::find($reportId);
        if (!$report) {
            return response()->json(['error' => 'No report found for this report ID'], 404);
        }

        // Get the sheet ID from the report
        $spreadsheetId = $report->sheet_id;
        if (!$spreadsheetId) {
            return response()->json(['error' => 'No sheet ID configured for this report'], 400);
        }

        // Get cell configurations for this report
        $cellConfigurations = $cellConfigService->getCellConfigurations($report->id, $tableId);

        // Initialize Google Sheets client
        $client = new Client();
        $client->setAuthConfig(storage_path('app/service_account.json'));
        $client->addScope(Sheets::SPREADSHEETS_READONLY);
        $service = new Sheets($client);

        // Initialize report data structure based on cell configurations
        $reportData = $this->initializeReportDataFromConfigurations($cellConfigurations);

        // Process data using cell configurations
        $reportData = $this->processDataWithConfigurations(
            $service,
            $spreadsheetId,
            $cellConfigurations,
            $reportData,
            $startDate,
            $endDate
        );

        // Calculate timeline percentage
        $timelinePercentageValue = "0%";
        if ($startDate && $endDate && $startDate->lte($endDate)) {
            $timelinePercentageValue = $this->calculateTimelinePercentage($startDate, $endDate) . "%";
        }

        // Format the data for display using cell configurations
        $reportData = $this->formatReportDataForDisplay($reportData, $cellConfigurations);

        $tableData = [];
        $chartData = [];
        $sheetName = "";
        $range = $sheetName . '!A3:XX';
        $firstMetric = "";
        $firstMetricIndex = 0;
        $secondMetric = "";
        $secondMetricIndex = 0;

        if ($tableId == "lazada-offsite-fb-conversion") {
            $sheetName = "Lazada - FB -ATC";
            $range = $sheetName . '!A3:XX';
            $firstMetric = "ATC";
            $firstMetricIndex = 5;
            $secondMetric = "%ATC";
            $secondMetricIndex = 13;
        } else if ($tableId == "lazada-offsite-fb-cpas") {
            $sheetName = "Lazada - FB - CPAS";
            $range = $sheetName . '!A3:XX';
            $firstMetric = "Purchase (Exclu. Cancellation)";
            $firstMetricIndex = 9;
            $secondMetric = "%Purchase";
            $secondMetricIndex = 14;
        } else if ($tableId == "lazada-onsite-search-discovery") {
            $sheetName = "Lazada - Onsite - Search/Discovery";
            $range = $sheetName . '!A3:XX';
            $firstMetric = "Purchase (Exclu. Cancellation)";
            $firstMetricIndex = 9;
            $secondMetric = "%Purchase";
            $secondMetricIndex = 14;
        } else if ($tableId == "lazada-onsite-affiliate") {
            $sheetName = "Lazada - Onsite - Affiliate";
            $range = $sheetName . '!A3:XX';
            $firstMetric = "Purchase (Exclu. Cancellation)";
            $firstMetricIndex = 9;
            $secondMetric = "%Purchase";
            $secondMetricIndex = 14;
        } else if ($tableId == "lazada-organic-untrackable") {
            $sheetName = "Lazada - Onsite - Organic";
            $range = $sheetName . '!A3:XX';
            $firstMetric = "Purchase (Exclu. Cancellation)";
            $firstMetricIndex = 9;
            $secondMetric = "%Purchase";
            $secondMetricIndex = 14;
        } else if ($tableId == "shopee-offsite-google-sem") {
            $sheetName = "Shopee - SEM -ATC";
            $range = $sheetName . '!A3:XX';
            $firstMetric = "ATC";
            $firstMetricIndex = 5;
            $secondMetric = "%ATC";
            $secondMetricIndex = 13;
        } else if ($tableId == "shopee-offsite-fb-conversion") {
            $sheetName = "Shopee - FB -ATC";
            $range = $sheetName . '!A3:XX';
            $firstMetric = "ATC";
            $firstMetricIndex = 5;
            $secondMetric = "%ATC";
            $secondMetricIndex = 13;
        } else if ($tableId == "shopee-offsite-fb-cpas") {
            $sheetName = "Shopee - FB - CPAS";
            $range = $sheetName . '!A3:XX';
            $firstMetric = "Purchase (Exclu. Cancellation)";
            $firstMetricIndex = 9;
            $secondMetric = "%Purchase";
            $secondMetricIndex = 14;
        } else if ($tableId == "shopee-offsite-youtube-trueview-action") {
            $sheetName = "Shopee - DV360 -ATC";
            $range = $sheetName . '!A3:XX';
            $firstMetric = "Purchase (Exclu. Cancellation)";
            $firstMetricIndex = 9;
            $secondMetric = "%Purchase";
            $secondMetricIndex = 14;
        } else if ($tableId == "shopee-onsite-search-discovery") {
            $sheetName = "Shopee - Onsite - Search/Discovery";
            $range = $sheetName . '!A3:XX';
            $firstMetric = "Purchase (Exclu. Cancellation)";
            $firstMetricIndex = 9;
            $secondMetric = "%Purchase";
            $secondMetricIndex = 14;
        } else if ($tableId == "shopee-onsite-affiliate") {
            $sheetName = "Shopee - Onsite - Affiliate";
            $range = $sheetName . '!A3:XX';
            $firstMetric = "Purchase (Exclu. Cancellation)";
            $firstMetricIndex = 9;
            $secondMetric = "%Purchase";
            $secondMetricIndex = 14;
        } else if ($tableId == "shopee-organic-untrackable") {
            $sheetName = "Shopee - Onsite - Organic";
            $range = $sheetName . '!A3:XX';
            $firstMetric = "Purchase (Exclu. Cancellation)";
            $firstMetricIndex = 9;
            $secondMetric = "%Purchase";
            $secondMetricIndex = 14;
        } else if ($tableId == "tiki-onsite-search-discovery") {
            $sheetName = "Tiki - Onsite - Search/Discovery";
            $range = $sheetName . '!A3:XX';
            $firstMetric = "Purchase (Exclu. Cancellation)";
            $firstMetricIndex = 9;
            $secondMetric = "%Purchase";
            $secondMetricIndex = 14;
        } else if ($tableId == "tiki-organic-untrackable") {
            $sheetName = "Tiki - Onsite - Organic";
            $range = $sheetName . '!A3:XX';
            $firstMetric = "Purchase (Exclu. Cancellation)";
            $firstMetricIndex = 9;
            $secondMetric = "%Purchase";
            $secondMetricIndex = 14;
        }

        if ($sheetName != "") {
            try {
                // Get daily performance data for the table
                $response = $service->spreadsheets_values->get($spreadsheetId, $range);
                $values = $response->getValues();

                if (!empty($values)) {
                    $tableData = $this->processLazadaFbConversionTableData($values, $startDate, $endDate);
                    $chartData = $this->processLazadaFbConversionChartData($values,  $firstMetric, $firstMetricIndex, $secondMetric, $secondMetricIndex, $startDate, $endDate,);
                }
            } catch (\Exception $e) {
                Log::error("Error fetching Lazada FB Conversion data: " . $e->getMessage());
            }
        }


        return response()->json([
            'message' => 'Report data processed successfully using cell configurations.',
            'report_id' => $report->id,
            'report_name' => $report->name,
            'sheet_id' => $spreadsheetId,
            'start_date_processed' => $startDate ? $startDate->toDateString() : 'N/A',
            'end_date_processed' => $endDate ? $endDate->toDateString() : 'N/A',
            'timeline_percentage' => $timelinePercentageValue,
            'data' => $reportData,
            'table' => $tableData,
            'chart' => $chartData
        ]);
    }

    /**
     * Initialize report data structure based on cell configurations.
     *
     * @param array $cellConfigurations
     * @return array
     */
    private function initializeReportDataFromConfigurations(array $cellConfigurations): array
    {
        // Start with a basic structure and expand based on configurations
        $reportData = [];

        // Extract unique channels and categories from cell identifiers
        foreach (array_keys($cellConfigurations) as $cellId) {
            $reportData[$cellId] = 0;
        }

        return $reportData;
    }

    /**
     * Process data using cell configurations.
     *
     * @param Sheets $service
     * @param string $spreadsheetId
     * @param array $cellConfigurations
     * @param array $reportData
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return array
     */
    private function processDataWithConfigurations(
        Sheets $service,
        string $spreadsheetId,
        array $cellConfigurations,
        array $reportData,
        ?Carbon $startDate,
        ?Carbon $endDate
    ): array {
        // Group configurations by source sheet for efficient processing
        $configsBySheet = [];
        $customFormulas = [];

        foreach ($cellConfigurations as $cellId => $config) {
            if ($config['useCustomFormula']) {
                $customFormulas[$cellId] = $config;
            } else if ($config['sourceSheet'] && $config['sourceMetricColumn']) {
                $sheetName = $config['sourceSheet'];
                if (!isset($configsBySheet[$sheetName])) {
                    $configsBySheet[$sheetName] = [];
                }
                $configsBySheet[$sheetName][$cellId] = $config;
            }
        }
        // Process each sheet
        foreach ($configsBySheet as $sheetName => $sheetConfigs) {
            $reportData = $this->processSheetData(
                $service,
                $spreadsheetId,
                $sheetName,
                $sheetConfigs,
                $reportData,
                $startDate,
                $endDate
            );
        }

        $reportData = $this->processCustomFormulasWithDependencies($customFormulas, $reportData);

        return $reportData;
    }

    /**
     * Process data from a specific sheet.
     *
     * @param Sheets $service
     * @param string $spreadsheetId
     * @param string $sheetName
     * @param array $sheetConfigs
     * @param array $reportData
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return array
     */
    private function processSheetData(
        Sheets $service,
        string $spreadsheetId,
        string $sheetName,
        array $sheetConfigs,
        array $reportData,
        ?Carbon $startDate,
        ?Carbon $endDate
    ): array {
        try {
            // Get data from the sheet (assuming data starts from row 3, with headers in rows 1-2)
            $range = $sheetName . '!A3:ZZ';
            $response = $service->spreadsheets_values->get($spreadsheetId, $range);
            $values = $response->getValues();

            Log::info("Processing sheet: {$sheetName}, rows found: " . count($values ?? []));

            if (empty($values)) {
                Log::info("No data found in sheet: {$sheetName}");
                return $reportData;
            }

            $processedRows = 0;
            $skippedRows = 0;

            // Process each row
            foreach ($values as $rowIndex => $row) {
                if (empty($row[0])) { // Skip rows without date
                    $skippedRows++;
                    continue;
                }

                // Parse date from first column
                try {
                    $rowDate = Carbon::parse($row[0]);
                } catch (\Exception $e) {
                    Log::warning("Could not parse date in sheet {$sheetName}, row " . ($rowIndex + 3) . ": " . $row[0]);
                    $skippedRows++;
                    continue;
                }

                // Check if date is within range
                if (($startDate && $rowDate->lt($startDate)) || ($endDate && $rowDate->gt($endDate))) {
                    $skippedRows++;
                    continue;
                }

                $processedRows++;

                // Process each configured cell for this sheet
                foreach ($sheetConfigs as $cellId => $config) {
                    $columnIndex = $config['colIndex'] ?? -1;
                    if (isset($row[$columnIndex])) {
                        $rawValue = $row[$columnIndex];
                        $value = $this->parseMetricValue($rawValue);

                        Log::debug("Processing cell {$cellId}: column {$config['sourceMetricColumn']} (index {$columnIndex}), raw value: '{$rawValue}', parsed: {$value}");

                        // Store raw numeric value for calculations, formatting happens later
                        $reportData = $this->updateReportDataValue($reportData, $cellId, $value, $config);
                    } else {
                        Log::debug("Column {$config['sourceMetricColumn']} (index {$columnIndex}) not found in row for cell {$cellId}");
                    }
                }
            }

            Log::info("Sheet {$sheetName} processing complete: {$processedRows} rows processed, {$skippedRows} rows skipped");
        } catch (\Google\Service\Exception $e) {
            Log::error("Google Sheets API error for sheet {$sheetName}: " . $e->getMessage());
        } catch (\Exception $e) {
            Log::error("Error processing sheet {$sheetName}: " . $e->getMessage());
        }

        return $reportData;
    }

    /**
     * Get debug information about processed sheets.
     *
     * @param array $cellConfigurations
     * @return array
     */
    private function getProcessedSheetsInfo(array $cellConfigurations): array
    {
        $sheetsInfo = [];
        foreach ($cellConfigurations as $cellId => $config) {
            if (!$config['useCustomFormula'] && $config['sourceSheet']) {
                $sheetName = $config['sourceSheet'];
                if (!isset($sheetsInfo[$sheetName])) {
                    $sheetsInfo[$sheetName] = [];
                }
                $sheetsInfo[$sheetName][] = [
                    'cell_id' => $cellId,
                    'column' => $config['sourceMetricColumn'],
                    'aggregation' => $config['aggregation']
                ];
            }
        }
        return $sheetsInfo;
    }

    /**
     * Get raw data before formatting for debugging.
     *
     * @param array $reportData
     * @return array
     */
    private function getRawDataBeforeFormatting(array $reportData): array
    {
        $rawData = [];
        foreach ($reportData as $channel => $categories) {
            foreach ($categories as $category => $metrics) {
                foreach ($metrics as $metric => $value) {
                    $rawData["{$channel}-{$category}-{$metric}"] = $value;
                }
            }
        }
        return $rawData;
    }

    /**
     * Test Google Sheets access.
     *
     * @param string $spreadsheetId
     * @return array
     */
    private function testGoogleSheetsAccess(string $spreadsheetId): array
    {
        try {
            $client = new Client();
            $client->setAuthConfig(storage_path('app/service_account.json'));
            $client->addScope(Sheets::SPREADSHEETS_READONLY);
            $service = new Sheets($client);

            // Try to get spreadsheet metadata
            $spreadsheet = $service->spreadsheets->get($spreadsheetId);
            $sheets = [];
            foreach ($spreadsheet->getSheets() as $sheet) {
                $sheets[] = $sheet->getProperties()->getTitle();
            }

            return [
                'access_successful' => true,
                'spreadsheet_title' => $spreadsheet->getProperties()->getTitle(),
                'available_sheets' => $sheets,
                'total_sheets' => count($sheets)
            ];
        } catch (\Exception $e) {
            return [
                'access_successful' => false,
                'error' => $e->getMessage(),
                'error_type' => get_class($e)
            ];
        }
    }

    /**
     * Parses a metric value string (e.g., "1.23M", "45K", "78.9%", "1,234.56") into a float.
     *
     * @param string $valueString
     * @return float
     */
    private function parseMetricValue(string $valueString): float
    {
        $valueString = trim($valueString);
        if (empty($valueString) || $valueString === '-' || $valueString === 'N/A') {
            return 0.0;
        }

        // Remove commas
        $cleanedValue = str_replace(',', '', $valueString);

        $multiplier = 1;
        if (stripos($cleanedValue, 'B') !== false) {
            $multiplier = 1000000000;
            $cleanedValue = str_ireplace('B', '', $cleanedValue);
        } elseif (stripos($cleanedValue, 'M') !== false) {
            $multiplier = 1000000;
            $cleanedValue = str_ireplace('M', '', $cleanedValue);
        } elseif (stripos($cleanedValue, 'K') !== false) {
            $multiplier = 1000;
            $cleanedValue = str_ireplace('K', '', $cleanedValue);
        } elseif (strpos($cleanedValue, '%') !== false) {
            // For percentages, we just take the numeric part.
            // If it should be a rate (e.g., 0.33 for 33%), adjust here.
            $cleanedValue = str_replace('%', '', $cleanedValue);
        }

        return floatval($cleanedValue) * $multiplier;
    }

    /**
     * Convert column letter to index (A=0, B=1, etc.)
     *
     * @param string $column
     * @return int
     */
    private function getColumnIndex(string $column): int
    {
        $column = strtoupper(trim($column));
        $index = 0;
        $length = strlen($column);

        for ($i = 0; $i < $length; $i++) {
            $index = $index * 26 + (ord($column[$i]) - ord('A') + 1);
        }

        return $index - 1; // Convert to 0-based index
    }

    /**
     * Update report data value based on configuration.
     *
     * @param array $reportData
     * @param string $cellId
     * @param float $value
     * @param array $config
     * @return array
     */
    private function updateReportDataValue(array $reportData, string $cellId, float $value, array $config): array
    {
        if (!isset($reportData[$cellId])) {
            $reportData[$cellId] = 0;
        }

        // Apply aggregation based on configuration
        switch ($config['aggregation']) {
            case 'sum':
                $reportData[$cellId] += $value;
                break;
            case 'average':
                // For average, we need to track count - simplified approach
                $reportData[$cellId] = ($reportData[$cellId] + $value) / 2;
                break;
            case 'first':
                if ($reportData[$cellId] == 0) {
                    $reportData[$cellId] = $value;
                }
                break;
            case 'last':
                $reportData[$cellId] = $value;
                break;
            case 'count':
                $reportData[$cellId]++;
                break;
            case 'direct':
            default:
                $reportData[$cellId] = $value;
                break;
        }

        return $reportData;
    }

    /**
     * Process custom formula for a cell.
     *
     * @param string $cellId
     * @param array $config
     * @param array $reportData
     * @return array
     */
    private function processCustomFormula(string $cellId, array $config, array $reportData): array
    {
        if (!isset($config['formula']) || empty($config['formula'])) {
            return $reportData;
        }

        Log::debug("Processing custom formula for cell {$cellId}", [
            'formula' => $config['formula'],
            'formulaDisplay' => $config['formulaDisplay'] ?? 'N/A'
        ]);

        try {
            // Build expression string and evaluate
            $expression = $this->buildFormulaExpression($config['formula'], $reportData);
            Log::debug("Built expression for {$cellId}: {$expression}");

            $formulaValue = $this->evaluateExpression($expression);
            Log::debug("Calculated formula value for {$cellId}: {$formulaValue}");

            $reportData[$cellId] = $formulaValue;
        } catch (\Exception $e) {
            Log::error("Error processing custom formula for {$cellId}: " . $e->getMessage(), [
                'formula' => $config['formula'],
                'error' => $e->getTraceAsString()
            ]);

            $reportData[$cellId] = "0";
        }

        return $reportData;
    }

    /**
     * Process custom formulas with dependency resolution.
     * If required metrics are missing, try to calculate them first.
     *
     * @param array $customFormulas
     * @param array $reportData
     * @return array
     */
    private function processCustomFormulasWithDependencies(array $customFormulas, array $reportData): array
    {
        $maxIterations = 5; // Prevent infinite loops
        $iteration = 0;
        $processedCells = [];

        while ($iteration < $maxIterations && count($processedCells) < count($customFormulas)) {
            $iteration++;
            Log::debug("Dependency resolution iteration {$iteration}");

            foreach ($customFormulas as $cellId => $config) {
                if (in_array($cellId, $processedCells)) {
                    continue; // Already processed
                }

                Log::debug("Checking dependencies for cell: {$cellId}");

                // Check if all required metrics are available
                $requiredMetrics = $this->extractRequiredMetrics($config);


                $isNotMissingMetric = empty(array_diff($requiredMetrics, array_keys($reportData)));

                if ($isNotMissingMetric) {
                    // All dependencies available, process this formula
                    Log::debug("All dependencies available for {$cellId}, processing...");
                    $reportData = $this->processCustomFormula($cellId, $config, $reportData);
                    $processedCells[] = $cellId;
                } else {
                    // Try to resolve missing metrics
                    Log::debug("Missing metrics for {$cellId}: " . implode(', ', array_diff($requiredMetrics, array_keys($reportData))));
                    $reportData = $this->tryToResolveMissingMetrics(array_diff($requiredMetrics, array_keys($reportData)), $reportData, $cellId);
                }
            }
        }

        // Process any remaining formulas (even if dependencies are missing)
        foreach ($customFormulas as $cellId => $config) {
            if (!in_array($cellId, $processedCells)) {
                Log::warning("Processing {$cellId} with missing dependencies");
                $reportData = $this->processCustomFormula($cellId, $config, $reportData);
            }
        }

        return $reportData;
    }

    /**
     * Extract required metrics from formula configuration.
     *
     * @param array $config
     * @return array
     */
    private function extractRequiredMetrics(array $config): array
    {
        $requiredMetrics = [];

        if (!isset($config['formula']) || !is_array($config['formula'])) {
            return $requiredMetrics;
        }

        foreach ($config['formula'] as $operand) {
            if (isset($operand['type']) && $operand['type'] === 'metric' && isset($operand['cell_identifier'])) {
                $requiredMetrics[] = $operand['cell_identifier'];
            }
        }

        return array_unique($requiredMetrics);
    }

    /**
     * Find missing metrics in report data.
     *
     * @param array $requiredMetrics
     * @param array $reportData
     * @return array
     */
    private function findMissingMetrics(array $requiredMetrics, array $reportData): array
    {
        $missingMetrics = [];

        foreach ($requiredMetrics as $metric) {
            $found = $this->findMetricValueInReportData($metric, $reportData);
            if ($found == 0) {
                $missingMetrics[] = $metric;
            }
        }

        return $missingMetrics;
    }

    /**
     * Try to resolve missing metrics by calculating them or finding alternatives.
     *
     * @param array $missingMetrics
     * @param array $reportData
     * @param string $targetCellId
     * @return array
     */
    private function tryToResolveMissingMetrics(array $missingMetrics, array $reportData, string $targetCellId): array
    {
        foreach ($missingMetrics as $metric) {
            Log::debug("Trying to resolve missing metric: {$metric} for cell: {$targetCellId}");

            // First, check if metric exists with different name in current data
            $existingValue = $this->findMetricWithAlternativeNames($metric, $reportData, $targetCellId);

            if ($existingValue > 0) {
                // Found existing metric with alternative name
                $reportData = $this->addResolvedMetricToReportData($metric, $existingValue, $reportData, $targetCellId);
                Log::debug("Found existing metric {$metric} with alternative name = {$existingValue}");
                continue;
            }

            // Try different strategies to resolve the metric
            $resolvedValue = $this->resolveMetricValue($metric, $reportData, $targetCellId);

            if ($resolvedValue !== null && $resolvedValue > 0) {
                // Add the resolved metric to report data
                $reportData = $this->addResolvedMetricToReportData($metric, $resolvedValue, $reportData, $targetCellId);
                Log::debug("Resolved metric {$metric} = {$resolvedValue}");
            } else {
                Log::warning("Could not resolve metric: {$metric}");
            }
        }

        return $reportData;
    }

    /**
     * Find metric with alternative names in current report data.
     *
     * @param string $metricName
     * @param array $reportData
     * @param string $targetCellId
     * @return float
     */
    private function findMetricWithAlternativeNames(string $metricName, array $reportData, string $targetCellId): float
    {
        // Extract channel from target cell ID
        $parts = explode('-', $targetCellId);
        $channel = $parts[0] ?? '';

        Log::debug("Looking for alternative names for '{$metricName}' in channel '{$channel}'");

        // Define specific mappings based on the current data structure
        $alternativeNames = [
            'Plan Spending' => ['spending', 'plan_spending', 'plan spending'],
            'Impression' => ['impression', 'impressions', 'traffic', 'plan_traffic'],
            'Click' => ['click', 'clicks', 'cpp', 'plan_cpp'],
        ];

        // Check if we have alternative names for this metric
        $searchNames = [$metricName];
        if (isset($alternativeNames[$metricName])) {
            $searchNames = array_merge($searchNames, $alternativeNames[$metricName]);
        }

        // Also check reverse mapping
        foreach ($alternativeNames as $canonical => $aliases) {
            if (in_array(strtolower($metricName), array_map('strtolower', $aliases))) {
                $searchNames[] = $canonical;
                $searchNames = array_merge($searchNames, $aliases);
            }
        }

        $searchNames = array_unique($searchNames);
        Log::debug("Searching for alternative names: " . implode(', ', $searchNames));

        // Search in current report data structure
        foreach ($searchNames as $searchName) {
            $value = $this->findMetricValueInReportData($searchName, $reportData);
            if ($value > 0) {
                Log::debug("Found '{$metricName}' as '{$searchName}' = {$value}");
                return $value;
            }
        }

        return 0;
    }

    /**
     * Build formula expression string from formula array.
     *
     * @param array $formula
     * @param array $reportData
     * @return string
     */
    private function buildFormulaExpression(array $formula, array $reportData): string
    {
        $expression = '';

        Log::debug("Building formula expression", [
            'formula' => $formula,
            'reportData_keys' => array_keys($reportData)
        ]);

        foreach ($formula as $index => $operand) {
            Log::debug("Processing operand {$index}", ['operand' => $operand]);

            if (!isset($operand['type']) || !isset($operand['value'])) {
                Log::warning("Operand missing type or value", ['operand' => $operand]);
                continue;
            }

            switch ($operand['type']) {
                case 'metric':
                    // Find metric value in reportData
                    $metricValue = $this->findMetricValueInReportData($operand['cell_identifier'], $reportData);
                    Log::debug("Metric '{$operand['value']}' resolved to: {$metricValue}");
                    $expression .= $metricValue;
                    break;

                case 'number':
                    $numberValue = floatval($operand['value']);
                    Log::debug("Number '{$operand['value']}' converted to: {$numberValue}");
                    $expression .= $numberValue;
                    break;

                case 'operator':
                    $operatorValue = ' ' . $operand['value'] . ' ';
                    Log::debug("Operator: '{$operatorValue}'");
                    $expression .= $operatorValue;
                    break;

                case 'parenthesis':
                    Log::debug("Parenthesis: '{$operand['value']}'");
                    $expression .= $operand['value'];
                    break;

                case 'cell_reference':
                    // Legacy support
                    $refValue = $this->getCellValueFromReportData($operand['value'], $reportData);
                    Log::debug("Cell reference '{$operand['value']}' resolved to: {$refValue}");
                    $expression .= $refValue;
                    break;

                default:
                    Log::warning("Unknown formula operand type: {$operand['type']}");
                    break;
            }
        }

        Log::debug("Final expression built: '{$expression}'");
        return $expression;
    }

    /**
     * Find metric value in report data by metric name.
     *
     * @param string $metricName
     * @param array $reportData
     * @return float
     */
    private function findMetricValueInReportData(string $metricName, array $reportData)
    {
        return $reportData[$metricName] ?? 0;
    }

    /**
     * Get all available metrics for debugging.
     */
    private function getAllAvailableMetrics(array $reportData): array
    {
        $allMetrics = [];
        foreach ($reportData as $channel => $categories) {
            foreach ($categories as $category => $metrics) {
                foreach ($metrics as $metric => $value) {
                    $allMetrics[] = "{$channel}.{$category}.{$metric}";
                }
            }
        }
        return $allMetrics;
    }

    /**
     * Get sample of report data structure for debugging.
     */
    private function getReportDataSample(array $reportData): array
    {
        $sample = [];
        foreach ($reportData as $channel => $categories) {
            $sample[$channel] = [];
            foreach ($categories as $category => $metrics) {
                $sample[$channel][$category] = array_keys($metrics);
                break; // Only show first category per channel
            }
            if (count($sample) >= 3) break; // Only show first 3 channels
        }
        return $sample;
    }

    /**
     * Get cell value from report data by cell identifier.
     *
     * @param string $cellId
     * @param array $reportData
     * @return float
     */
    private function getCellValueFromReportData(string $cellId, array $reportData): float
    {
        $parts = explode('-', $cellId);
        if (count($parts) >= 3) {
            $channel = $parts[0];
            $category = $parts[1];
            $metric = implode('_', array_slice($parts, 2));

            if (isset($reportData[$channel][$category][$metric])) {
                return $this->parseMetricValue($reportData[$channel][$category][$metric]);
            }
        }

        return 0;
    }

    /**
     * Resolve metric value using various strategies.
     *
     * @param string $metric
     * @param array $reportData
     * @param string $targetCellId
     * @return float|null
     */
    private function resolveMetricValue(string $metric, array $reportData, string $targetCellId): ?float
    {
        Log::debug("Resolving metric: {$metric} for target: {$targetCellId}");

        // Strategy 1: Try common metric calculations based on target cell
        if (stripos($targetCellId, 'revenue') !== false) {
            return $this->calculateRevenueMetrics($metric, $reportData, $targetCellId);
        }

        // Strategy 2: Try to find similar metrics in other channels
        if (stripos($metric, 'click') !== false || stripos($metric, 'impression') !== false) {
            return $this->calculateClickImpressionMetrics($metric, $reportData, $targetCellId);
        }

        // Strategy 3: Use default values based on metric type
        return $this->getDefaultMetricValue($metric, $targetCellId);
    }

    /**
     * Calculate revenue-related metrics.
     *
     * @param string $metric
     * @param array $reportData
     * @param string $targetCellId
     * @return float|null
     */
    private function calculateRevenueMetrics(string $metric, array $reportData, string $targetCellId): ?float
    {
        // Extract channel from target cell ID (e.g., "shopee-plan-revenueExCancel" -> "shopee")
        $parts = explode('-', $targetCellId);
        $channel = $parts[0] ?? '';

        Log::debug("Calculating revenue metrics for channel: {$channel}");

        // Try to find spending for this channel
        $spending = $this->findMetricInChannel($channel, ['spending', 'plan spending', 'plan_spending'], $reportData);

        if ($spending > 0) {
            // Use different strategies based on the missing metric
            if (stripos($metric, 'click') !== false) {
                // Estimate clicks based on spending (example: $1 = 10 clicks)
                $estimatedClicks = $spending / 100; // 1M spending = 10K clicks
                Log::debug("Estimated clicks for {$channel}: {$estimatedClicks}");
                return $estimatedClicks;
            }

            if (stripos($metric, 'impression') !== false) {
                // Estimate impressions based on spending (example: $1 = 1000 impressions)
                $estimatedImpressions = $spending * 10; // 1M spending = 10M impressions
                Log::debug("Estimated impressions for {$channel}: {$estimatedImpressions}");
                return $estimatedImpressions;
            }

            if (stripos($metric, 'roas') !== false) {
                // Use industry average ROAS based on channel
                $defaultRoas = $this->getDefaultRoas($channel);
                Log::debug("Using default ROAS for {$channel}: {$defaultRoas}");
                return $defaultRoas;
            }
        }

        return null;
    }

    /**
     * Find metric value in specific channel.
     *
     * @param string $channel
     * @param array $metricNames
     * @param array $reportData
     * @return float
     */
    private function findMetricInChannel(string $channel, array $metricNames, array $reportData): float
    {
        if (!isset($reportData[$channel])) {
            return 0;
        }

        foreach ($reportData[$channel] as $category => $metrics) {
            foreach ($metricNames as $metricName) {
                foreach ($metrics as $metric => $value) {
                    if (stripos($metric, $metricName) !== false) {
                        $parsedValue = $this->parseMetricValue($value);
                        Log::debug("Found {$metricName} in {$channel}.{$category}: {$parsedValue}");
                        return $parsedValue;
                    }
                }
            }
        }

        return 0;
    }

    /**
     * Get default ROAS based on channel.
     *
     * @param string $channel
     * @return float
     */
    private function getDefaultRoas(string $channel): float
    {
        $defaultRoas = [
            'shopee' => 12.0,
            'lazada' => 15.0,
            'tiki' => 8.0,
        ];

        return $defaultRoas[strtolower($channel)] ?? 10.0;
    }

    /**
     * Calculate click/impression metrics.
     *
     * @param string $metric
     * @param array $reportData
     * @param string $targetCellId
     * @return float|null
     */
    private function calculateClickImpressionMetrics(string $metric, array $reportData, string $targetCellId): ?float
    {
        $parts = explode('-', $targetCellId);
        $channel = $parts[0] ?? '';

        // Try to find spending to estimate clicks/impressions
        $spending = $this->findMetricInChannel($channel, ['spending', 'plan spending'], $reportData);

        if ($spending > 0) {
            if (stripos($metric, 'click') !== false) {
                return $spending / 100; // 1M spending = 10K clicks
            }
            if (stripos($metric, 'impression') !== false) {
                return $spending * 10; // 1M spending = 10M impressions
            }
        }

        // Use default values
        if (stripos($metric, 'click') !== false) {
            return 10000; // Default 10K clicks
        }
        if (stripos($metric, 'impression') !== false) {
            return 1000000; // Default 1M impressions
        }

        return null;
    }

    /**
     * Get default metric value.
     *
     * @param string $metric
     * @param string $targetCellId
     * @return float|null
     */
    private function getDefaultMetricValue(string $metric, string $targetCellId): ?float
    {
        // Return reasonable defaults for common metrics
        $defaults = [
            'click' => 10000,
            'impression' => 1000000,
            'ctr' => 0.01,
            'cpc' => 0.5,
            'roas' => 10.0,
        ];

        foreach ($defaults as $key => $value) {
            if (stripos($metric, $key) !== false) {
                Log::debug("Using default value for {$metric}: {$value}");
                return $value;
            }
        }

        return null;
    }

    /**
     * Add resolved metric to report data.
     *
     * @param string $metric
     * @param float $value
     * @param array $reportData
     * @param string $targetCellId
     * @return array
     */
    private function addResolvedMetricToReportData(string $metric, float $value, array $reportData, string $targetCellId): array
    {
        // Extract channel from target cell ID
        $parts = explode('-', $targetCellId);
        $channel = $parts[0] ?? 'default';
        $category = $parts[1] ?? 'calculated';

        // Add the metric to the appropriate location
        if (!isset($reportData[$channel])) {
            $reportData[$channel] = [];
        }
        if (!isset($reportData[$channel][$category])) {
            $reportData[$channel][$category] = [];
        }

        // Use the metric name as key
        $metricKey = strtolower(str_replace(' ', '_', $metric));
        $reportData[$channel][$category][$metricKey] = $this->formatNumberToString($value);

        Log::debug("Added resolved metric to report data: {$channel}.{$category}.{$metricKey} = {$value}");

        return $reportData;
    }

    /**
     * Evaluate mathematical expression safely.
     *
     * @param string $expression
     * @return float
     */
    private function evaluateExpression(string $expression): float
    {
        // Clean the expression
        $expression = trim($expression);

        if (empty($expression)) {
            return 0;
        }

        // Check for division by zero before evaluation
        if (preg_match('/\/\s*0(?:\s|$|[\+\-\*\/])/', $expression)) {
            Log::warning("Division by zero detected in expression: {$expression}");
            return 0;
        }

        // Simple validation - only allow numbers, operators, parentheses, and spaces
        if (!preg_match('/^[0-9+\-*\/\.\(\)\s]+$/', $expression)) {
            Log::warning("Invalid expression: {$expression}");
            return 0;
        }

        try {
            // Use eval() carefully with validation
            $result = eval("return {$expression};");

            if (!is_numeric($result)) {
                Log::warning("Expression evaluation returned non-numeric result: {$result}");
                return 0;
            }

            // Additional check for infinity or NaN
            if (!is_finite($result)) {
                Log::warning("Expression evaluation returned infinite or NaN: {$result}");
                return 0;
            }

            return floatval($result);
        } catch (\Exception $e) {
            Log::error("Error evaluating expression '{$expression}': " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Format metric value according to display format.
     *
     * @param float $value
     * @param array $config
     * @return string
     */
    private function formatMetricValue(float $value, array $config): string
    {
        $displayFormat = $config['displayFormat'] ?? 'default';
        $valueType = $config['valueType'] ?? 'number';
        if ($valueType == 'percent') {
            return round($value) . '%';
        }

        switch ($displayFormat) {
            case 'number_0':
                return $this->formatNumberWithSuffix($value, 0);

            case 'number_2':
                return $this->formatNumberWithSuffix($value, 2);

            case 'percent_0':
                // Convert decimal to percentage (0.515 -> 52%)
                return round($value * 100) . '%';

            case 'percent_2':
                // Convert decimal to percentage with 2 decimals (0.515 -> 51.50%)
                return number_format($value * 100, 2) . '%';

            case 'currency_vnd':
                return number_format($value, 0) . ' VND';

            case 'currency_usd':
                return '$' . number_format($value, 2);

            case 'short_number_k':
                if ($value >= 1000) {
                    return round($value / 1000, 1) . 'K';
                }
                return (string) $value;

            case 'short_number_m':
                if ($value >= 1000000) {
                    return round($value / 1000000, 1) . 'M';
                } elseif ($value >= 1000) {
                    return round($value / 1000, 1) . 'K';
                }
                return (string) $value;

            case 'short_number_b':
                if ($value >= 1000000000) {
                    return round($value / 1000000000, 1) . 'B';
                } elseif ($value >= 1000000) {
                    return round($value / 1000000, 1) . 'M';
                } elseif ($value >= 1000) {
                    return round($value / 1000, 1) . 'K';
                }
                return (string) $value;

            case 'default':
            case 'general':
            default:
                // For default format, use smart formatting with B/M/K suffixes for large numbers
                return $this->formatNumberToString($value);
        }
    }

    /**
     * Format report data for display using cell configurations.
     *
     * @param array $reportData
     * @param array $cellConfigurations
     * @return array
     */
    private function formatReportDataForDisplay(array $reportData, array $cellConfigurations = []): array
    {
        foreach ($reportData as $cellId => &$value) {

            $config = $cellConfigurations[$cellId] ?? null;

            if ($config && isset($config['displayFormat'])) {
                $value = $this->formatMetricValue(is_numeric($value) ? (float)$value : 0, $config);
            } else {
                $value = $this->formatNumberToString($value);
            }
        }

        return $reportData;
    }

    /**
     * Formats a number into a string with 'B', 'M', 'K' suffixes.
     * e.g., 1000000 => "1M", 1000 => "1K", 2567 => "2.57K"
     *
     * @param float $number
     * @return string
     */
    private function formatNumberToString(float $number): string
    {
        if ($number == 0) {
            return '0';
        }

        if (abs($number) >= 1000000000) {
            return round($number / 1000000000, 2) . 'B';
        }
        if (abs($number) >= 1000000) {
            return round($number / 1000000, 2) . 'M';
        }
        if (abs($number) >= 1000) {
            return round($number / 1000, 2) . 'K';
        }

        // For numbers less than 1000, decide if you want decimal places
        // For simplicity, let's round to 2 decimal places if it's not an integer
        // or show as integer if it is.
        return round($number, 2); // Or use number_format for more control
    }

    /**
     * Formats a number with B/M/K suffixes and specified decimal places.
     * e.g., formatNumberWithSuffix(1000000, 0) => "1M"
     *       formatNumberWithSuffix(1000000, 2) => "1.00M"
     *       formatNumberWithSuffix(1500000, 1) => "1.5M"
     *
     * @param float $number
     * @param int $decimals
     * @return string
     */
    private function formatNumberWithSuffix(float $number, int $decimals): string
    {
        if ($number == 0) {
            return '0';
        }

        if (abs($number) >= 1000000000) {
            return number_format($number / 1000000000, $decimals) . 'B';
        }
        if (abs($number) >= 1000000) {
            return number_format($number / 1000000, $decimals) . 'M';
        }
        if (abs($number) >= 1000) {
            return number_format($number / 1000, $decimals) . 'K';
        }

        // For numbers less than 1000, use the specified decimal places
        return number_format($number, $decimals);
    }

    /**
     * Calculates the timeline percentage based on start and end dates.
     * Each month's contribution is (days in range for that month / total days in that month) * 100.
     * The sum of these monthly percentages is then rounded.
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return float
     */
    private function calculateTimelinePercentage(Carbon $startDate, Carbon $endDate): float
    {
        $totalPercentage = 0;
        $currentMonthIterator = $startDate->copy()->startOfMonth(); // Iterate by month start

        while ($currentMonthIterator->lte($endDate)) {
            $daysInCurrentMonth = $currentMonthIterator->daysInMonth;
            $daysCountedThisMonth = 0;

            // Determine the effective start and end for this iteration's month within the overall range
            $effectiveStartForMonth = $startDate->gt($currentMonthIterator) ? $startDate : $currentMonthIterator->copy();
            $endOfCurrentMonth = $currentMonthIterator->copy()->endOfMonth();
            $effectiveEndForMonth = $endDate->lt($endOfCurrentMonth) ? $endDate : $endOfCurrentMonth;

            // Ensure we are still within the current iterated month for calculation
            if ($effectiveStartForMonth->month == $currentMonthIterator->month && $effectiveEndForMonth->month == $currentMonthIterator->month) {
                if ($effectiveStartForMonth->lte($effectiveEndForMonth)) {
                    $daysCountedThisMonth = $effectiveEndForMonth->diffInDays($effectiveStartForMonth) + 1;
                }
            }

            if ($daysInCurrentMonth > 0 && $daysCountedThisMonth > 0) {
                $totalPercentage += ($daysCountedThisMonth / $daysInCurrentMonth) * 100;
            }

            // Move to the next month
            $currentMonthIterator->addMonthNoOverflow();
        }

        return round($totalPercentage);
    }

    /**
     * Fetches weekly comments from the "Copy of Weekly Comment" sheet.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWeeklyComments(Request $request, EcomReport $report)
    {
        $spreadsheetId = $report->sheet_id ?? '';

        if (!$spreadsheetId) {
            return response()->json(['error' => 'Provide sheetId'], 400);
        }

        $cacheKey = $report->id . '_weekly_comments';
        $cacheDuration = 86400;

        try {
            if ($spreadsheetId) {
                $commentsData = Cache::remember($cacheKey, $cacheDuration, function () use ($spreadsheetId) {
                    Log::info('Fetching weekly comments from Google Sheets API for spreadsheet: ' . $spreadsheetId);
                    $client = new Client();
                    $client->setAuthConfig(storage_path('app/service_account.json'));
                    $client->addScope(Sheets::SPREADSHEETS_READONLY);

                    $service = new Sheets($client);
                    // $spreadsheetId is already a class property, so we can use $spreadsheetId
                    $sheetName = 'Copy of Weekly Comment'; // The specific sheet for comments
                    $range = $sheetName . '!A2:B'; // Assuming Week is in Col A, Comment in Col B, data starts from row 2

                    $response = $service->spreadsheets_values->get($spreadsheetId, $range);
                    $values = $response->getValues();

                    if (empty($values)) {
                        return [];
                    }

                    $formattedComments = [];
                    foreach ($values as $row) {
                        if (!empty($row[0]) && !empty($row[1])) { // Ensure both week and comment are present
                            $formattedComments[] = [
                                'week' => $row[0],
                                'comment' => $row[1],
                            ];
                        }
                    }
                    return $formattedComments;
                });

                return response()->json([
                    'message' => 'Weekly comments fetched successfully.',
                    'comments' => $commentsData,
                ]);
            }
            return response()->json([
                'message' => 'No have sheetId',
                'comments' => [],
            ]);
        } catch (\Google\Service\Exception $e) {
            logger()->error("Google Sheets API error while fetching weekly comments: " . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch comments due to Google Sheets API error. Details: ' . $e->getMessage()], 500);
        } catch (\Exception $e) {
            logger()->error("Error fetching weekly comments: " . $e->getMessage());
            return response()->json(['error' => 'An unexpected error occurred while fetching comments. Details: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get all cell configurations for a report.
     *
     * @param Request $request
     * @param EcomReport $report
     * @param EcomReportSettingService $settingService
     * @return JsonResponse
     */
    public function getCellConfigurations(Request $request, EcomReport $report, EcomReportCellConfigService $cellConfigService): JsonResponse
    {
        $request->validate([
            'table_id' => 'nullable|string',
        ]);

        try {
            $reportId = $report->id;
            $tableId = $request->input('table_id');

            $configurations = $cellConfigService->getCellConfigurations($reportId, $tableId);

            return response()->json([
                'success' => true,
                'message' => 'Cell configurations retrieved successfully',
                'configurations' => $configurations
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting cell configurations', [
                'error' => $e->getMessage(),
                'report_id' => $report->id
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve cell configurations',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Save a cell configuration.
     *
     * @param Request $request
     * @param EcomReportCellConfigService $cellConfigService
     * @return JsonResponse
     */
    public function saveCellConfiguration(Request $request, EcomReport $report, EcomReportCellConfigService $cellConfigService): JsonResponse
    {
        $request->validate([
            'config' => 'required|array',
            'config.source_sheet' => 'nullable|string',
            'config.source_metric_column' => 'nullable|string',
            'config.value_type' => 'required|string|in:percent,number',
            'config.display_format' => 'nullable|string',
            'config.aggregation' => 'nullable|string', // Made nullable for custom formulas
            'config.use_custom_formula' => 'required|boolean',
            'config.formula' => 'nullable|array',
            'config.table_id' => 'nullable|string',
            'config.col_index' => 'nullable|integer',
            'config.row_index' => 'nullable|integer',
        ]);

        try {
            $reportId = $report->id;
            $config = $request->input('config');

            // Save configuration using new service
            $result = $cellConfigService->saveCellConfiguration($reportId, $config);

            if (!$result['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 422);
            }

            $configData = $result['data'];

            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'data' => $result['data']
            ]);
        } catch (\Exception $e) {
            Log::error('Error saving cell configuration', [
                'error' => $e->getMessage(),
                'report_id' => $request->input('report_id'),
                'cell_id' => $request->input('cell_id')
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to save cell configuration',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get configuration options for the modal.
     *
     * @param EcomReportCellConfigService $cellConfigService
     * @return JsonResponse
     */
    public function getConfigurationOptions(EcomReportCellConfigService $cellConfigService): JsonResponse
    {
        try {
            $options = [
                'valueTypes' => \App\Constants\EcomReportCellConfig::getValueTypes(),
                'displayFormats' => \App\Constants\EcomReportCellConfig::getDisplayFormats(),
                'aggregationTypes' => \App\Constants\EcomReportCellConfig::getAggregationTypes(),
                'statusOptions' => \App\Constants\EcomReportCellConfig::getStatusOptions(),
            ];

            return response()->json([
                'success' => true,
                'message' => 'Configuration options retrieved successfully',
                'options' => $options
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting configuration options', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve configuration options',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a cell configuration.
     *
     * @param Request $request
     * @param EcomReportCellConfigService $cellConfigService
     * @return JsonResponse
     */
    public function deleteCellConfiguration(Request $request, EcomReportCellConfigService $cellConfigService): JsonResponse
    {
        $request->validate([
            'report_id' => 'required|integer|exists:ecom_reports,id',
            'cell_id' => 'required|string'
        ]);

        try {
            $reportId = $request->input('report_id');
            $cellId = $request->input('cell_id');

            $result = $cellConfigService->deleteCellConfiguration($reportId, $cellId);

            return response()->json([
                'success' => $result['success'],
                'message' => $result['message']
            ]);
        } catch (\Exception $e) {
            Log::error('Error deleting cell configuration', [
                'error' => $e->getMessage(),
                'report_id' => $request->input('report_id'),
                'cell_id' => $request->input('cell_id')
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete cell configuration',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Auto-detect and update col_index for all configurations based on Google Sheets headers.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function autoDetectColIndex(Request $request)
    {
        try {
            $reportId = $request->input('report_id');

            if (!$reportId) {
                return response()->json(['error' => 'Report ID is required'], 400);
            }

            $result = $this->detectAndUpdateColIndexes($reportId);

            return response()->json([
                'success' => true,
                'message' => 'Column indexes auto-detected and updated successfully',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('Error auto-detecting col_index: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Failed to auto-detect column indexes: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Detect and update col_index for all configurations.
     *
     * @param int $reportId
     * @return array
     */
    private function detectAndUpdateColIndexes(int $reportId): array
    {
        $report = EcomReport::findOrFail($reportId);
        $configs = EcomReportCellConfig::where('ecom_report_id', $reportId)->get();

        $result = [
            'total_configs' => $configs->count(),
            'updated_configs' => 0,
            'skipped_configs' => 0,
            'errors' => [],
            'updates' => []
        ];

        // Group configs by sheet to minimize API calls
        $configsBySheet = $configs->groupBy('source_sheet');

        foreach ($configsBySheet as $sheetName => $sheetConfigs) {
            if (empty($sheetName)) {
                continue;
            }

            try {
                // Get headers for this sheet
                $headers = $this->getSheetHeaders($report->sheet_id, $sheetName);

                if (empty($headers)) {
                    Log::warning("No headers found for sheet: {$sheetName}");
                    continue;
                }

                // Update col_index for each config in this sheet
                foreach ($sheetConfigs as $config) {
                    $updateResult = $this->updateConfigColIndex($config, $headers);

                    if ($updateResult['updated']) {
                        $result['updated_configs']++;
                        $result['updates'][] = $updateResult;
                    } else {
                        $result['skipped_configs']++;
                        if (!empty($updateResult['error'])) {
                            $result['errors'][] = $updateResult;
                        }
                    }
                }
            } catch (\Exception $e) {
                Log::error("Error processing sheet {$sheetName}: " . $e->getMessage());
                $result['errors'][] = [
                    'sheet' => $sheetName,
                    'error' => $e->getMessage()
                ];
            }
        }

        Log::info('Col_index auto-detection completed', $result);
        return $result;
    }

    /**
     * Get headers from Google Sheets.
     *
     * @param string $spreadsheetId
     * @param string $sheetName
     * @return array
     */
    private function getSheetHeaders(string $spreadsheetId, string $sheetName): array
    {
        try {
            $client = new Client();
            $client->setAuthConfig(storage_path('app/service_account.json'));
            $client->addScope(Sheets::SPREADSHEETS_READONLY);
            $service = new Sheets($client);

            // Get headers from row 1 and row 2 (some sheets have multi-row headers)
            $range = $sheetName . '!1:2';
            $response = $service->spreadsheets_values->get($spreadsheetId, $range);
            $values = $response->getValues();

            if (empty($values)) {
                return [];
            }

            // Combine headers from both rows if needed
            $headers = [];
            $row1 = $values[0] ?? [];
            $row2 = $values[1] ?? [];

            for ($i = 0; $i < max(count($row1), count($row2)); $i++) {
                $header1 = $row1[$i] ?? '';
                $header2 = $row2[$i] ?? '';

                // Combine headers with newline if both exist
                if (!empty($header1) && !empty($header2) && $header1 !== $header2) {
                    $headers[$i] = $header1 . "\n" . $header2;
                } else {
                    $headers[$i] = !empty($header1) ? $header1 : $header2;
                }
            }

            Log::debug("Headers found for sheet {$sheetName}", [
                'header_count' => count($headers),
                'sample_headers' => array_slice($headers, 0, 5)
            ]);

            return $headers;
        } catch (\Exception $e) {
            Log::error("Error getting headers for sheet {$sheetName}: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Update col_index for a configuration based on headers.
     *
     * @param EcomReportCellConfig $config
     * @param array $headers
     * @return array
     */
    private function updateConfigColIndex(EcomReportCellConfig $config, array $headers): array
    {
        $result = [
            'cell_id' => $config->cell_identifier,
            'updated' => false,
            'old_col_index' => $config->col_index,
            'new_col_index' => null,
            'error' => null
        ];

        // Skip if col_index already exists
        if ($config->col_index !== null) {
            $result['error'] = 'col_index already exists';
            return $result;
        }

        // Skip if no source column specified
        if (empty($config->source_metric_column)) {
            $result['error'] = 'No source_metric_column specified';
            return $result;
        }

        // Find matching header
        $targetColumn = $config->source_metric_column;
        $foundIndex = null;

        // First pass: Try exact matches only
        foreach ($headers as $index => $header) {
            if (trim($header) === trim($targetColumn)) {
                $foundIndex = $index;
                break;
            }
        }

        // Second pass: Try case-insensitive matches if no exact match found
        if ($foundIndex === null) {
            foreach ($headers as $index => $header) {
                if (strcasecmp(trim($header), trim($targetColumn)) === 0) {
                    $foundIndex = $index;
                    break;
                }
            }
        }

        // Third pass: Try partial matches only if no exact/case-insensitive match found
        // Only for headers with line breaks or complex formatting
        if ($foundIndex === null) {
            foreach ($headers as $index => $header) {
                // Only do partial matching if header contains newlines or target is significantly different
                if (strpos($header, "\n") !== false || strpos($targetColumn, "\n") !== false) {
                    if (stripos($header, $targetColumn) !== false || stripos($targetColumn, $header) !== false) {
                        $foundIndex = $index;
                        break;
                    }
                }
            }
        }

        if ($foundIndex !== null) {
            // Update the configuration
            $config->col_index = $foundIndex;
            $config->save();

            $result['updated'] = true;
            $result['new_col_index'] = $foundIndex;

            Log::info("Updated col_index for {$config->cell_identifier}", [
                'column' => $targetColumn,
                'col_index' => $foundIndex,
                'header_found' => $headers[$foundIndex]
            ]);
        } else {
            $result['error'] = "Column '{$targetColumn}' not found in headers";

            Log::warning("Column not found for {$config->cell_identifier}", [
                'target_column' => $targetColumn,
                'available_headers' => array_values($headers)
            ]);
        }

        return $result;
    }

    /**
     * Process Lazada FB Conversion table data from Google Sheets.
     *
     * @param array $values
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return array
     */
    private function processLazadaFbConversionTableData(array $values, ?Carbon $startDate, ?Carbon $endDate): array
    {
        $tableData = [];

        foreach ($values as $row) {
            if (empty($row[0])) {
                continue;
            }

            try {
                $rowDate = Carbon::parse($row[0]);

                // Check if date is within range
                if (($startDate && $rowDate->lt($startDate)) || ($endDate && $rowDate->gt($endDate))) {
                    continue;
                }

                $tableData[] = [
                    'date' => $rowDate->format('M j, Y'),
                    'impression' => $row[1],
                    'click' => $row[2],
                    'outbound_click' => $row[3],
                    'traffic' => $row[4],
                    'atc' => $row[5],
                    'atc_value' => $row[6],
                    'purchase_excl' => $row[7],
                    'purchase_excl_value' => $row[8],
                    'ctr' => $row[9],
                    'ocr' => $row[10],
                    'visit_rate' => $row[11],
                    'atc_rate' => $row[12],
                    'purchase_rate' => $row[13],
                    'aov' => $row[14],
                    'revenue_excl' => $row[15],
                    'roas_excl' => $row[16],
                    'cancellation' => $row[17],
                    'revenue_incl' => $row[18],
                    'roas_incl' => $row[19],
                    'unit_cost' => $row[20],
                    'spending' => $row[21]
                ];
            } catch (\Exception $e) {
                Log::warning("Could not parse date in Lazada FB Conversion data: " . $row[0]);
                continue;
            }
        }

        return $tableData;
    }

    /**
     * Process Lazada FB Conversion chart data from Google Sheets.
     *
     * @param array $values
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @return array
     */
    private function processLazadaFbConversionChartData(array $values, $firstMetric, $firstMetricIndex, $secondMetric, $secondMetricIndex, ?Carbon $startDate, ?Carbon $endDate): array
    {
        $chartData = [
            'categories' => [],
            'series' => [
                [
                    'name' => $firstMetric,
                    'data' => []
                ],
                [
                    'name' => $secondMetric,
                    'data' => []
                ],
            ]
        ];

        foreach ($values as $row) {
            if (empty($row[0])) {
                continue;
            }

            try {
                $rowDate = Carbon::parse($row[0]);

                // Check if date is within range
                if (($startDate && $rowDate->lt($startDate)) || ($endDate && $rowDate->gt($endDate))) {
                    continue;
                }

                $chartData['categories'][] = $rowDate->format('M j');
                $chartData['series'][0]['data'][] = $this->parseMetricValue($row[$firstMetricIndex] ?? 0);
                $chartData['series'][1]['data'][] = $this->parseMetricValue($row[$secondMetricIndex] ?? 0);
            } catch (\Exception $e) {
                Log::warning("Could not parse date in Lazada FB Conversion chart data: " . $row[0]);
                continue;
            }
        }

        return $chartData;
    }
}
