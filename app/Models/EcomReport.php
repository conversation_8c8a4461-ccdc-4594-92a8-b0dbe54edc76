<?php

namespace App\Models;

use App\Common\Constant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class EcomReport extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'ecom_reports';

    /**
     * fillable
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'type',
        'sheet_id',
        'status',
        'client_id',
        'created_by',
        'updated_by'
    ];

    /**
     * Get Creator
     *
     * @return BelongsTo
     */
    public function creator(): BelongsTo
    {
        return $this->BelongsTo(User::class, 'created_by');
    }

    /**
     * Get Updater
     *
     * @return BelongsTo
     */
    public function updater(): BelongsTo
    {
        return $this->BelongsTo(User::class, 'updated_by');
    }

    /**
     * Get the cell configurations for this report.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function cellConfigs()
    {
        return $this->hasMany(EcomReportCellConfig::class, 'ecom_report_id');
    }

    /**
     * Get active cell configurations for this report.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function activeCellConfigs()
    {
        return $this->cellConfigs()->active();
    }

    /**
     * Get cell configuration by identifier.
     */
    public function getCellConfig(string $cellIdentifier): ?EcomReportCellConfig
    {
        return $this->activeCellConfigs()->where('cell_identifier', $cellIdentifier)->first();
    }

    /**
     * Get all cell configurations as an associative array.
     */
    public function getCellConfigsArray(): array
    {
        return $this->activeCellConfigs()
            ->get()
            ->keyBy('cell_identifier')
            ->map(function ($config) {
                return [
                    'id' => $config->id,
                    'table_id' => $config->table_id,
                    'row_index' => $config->row_index,
                    'col_index' => $config->col_index,
                    'use_custom_formula' => $config->use_custom_formula,
                    'source_sheet' => $config->source_sheet,
                    'source_metric_column' => $config->source_metric_column,
                    'source_identifier' => $config->source_identifier,
                    'value_type' => $config->value_type,
                    'display_format' => $config->display_format,
                    'aggregation' => $config->aggregation_type,
                    'formula' => $config->formula,
                    'description' => $config->description,
                ];
            })
            ->toArray();
    }
}
