<?php

namespace App\Services;

use App\Models\EcomReport;
use App\Models\EcomReportCellConfig;
use App\Constants\EcomReportCellConfig as CellConfigConstants;
use Google\Client;
use Google\Service\Sheets;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class EcomReportCellConfigService
{
    /**
     * Get cell configurations for a specific report (compatible with old service).
     *
     * @param int $reportId
     * @return array
     */
    public function getCellConfigurations(int $reportId, $tableId = "overall"): array
    {
        $cellConfigs = EcomReportCellConfig::forReport($reportId)
            ->where('table_id', $tableId)
            ->active()
            ->get();

        $configurations = [];
        foreach ($cellConfigs as $config) {
            $configurations[$config->cell_identifier] = [
                'id' => $config->id,
                'tableId' => $config->table_id,
                'cellIdentifier' => $config->cell_identifier,
                'rowIndex' => $config->row_index,
                'colIndex' => $config->col_index,
                'sourceSheet' => $config->source_sheet,
                'sourceMetricColumn' => $config->source_metric_column,
                'sourceIdentifier' => $config->source_identifier,
                'valueType' => $config->value_type,
                'displayFormat' => $config->display_format,
                'aggregation' => $config->aggregation_type,
                'useCustomFormula' => $config->use_custom_formula,
                'formula' => $config->formula ?? [],
                'formulaDisplay' => $config->formula_display,
                'description' => $config->description,
            ];
        }

        return $configurations;
    }

    /**
     * Get formula dependencies for a cell configuration.
     *
     * @param int $reportId
     * @param string $cellIdentifier
     * @return array
     */
    public function getFormulaDependencies(int $reportId, string $cellIdentifier): array
    {
        $cellConfig = EcomReportCellConfig::forReport($reportId)
            ->forCell($cellIdentifier)
            ->first();

        if (!$cellConfig || !$cellConfig->use_custom_formula || empty($cellConfig->formula)) {
            return [];
        }

        $dependencies = [];
        $parts = explode('-', $cellIdentifier);
        $channel = $parts[0] ?? 'default';
        $category = $parts[1] ?? 'plan';

        foreach ($cellConfig->formula as $element) {
            if (is_array($element) && isset($element['type']) && $element['type'] === 'metric') {
                $metricName = $element['value'];
                $dependencyCellId = $this->generateDependencyCellId($channel, $category, $metricName);

                $dependencyConfig = EcomReportCellConfig::forReport($reportId)
                    ->forCell($dependencyCellId)
                    ->first();

                if ($dependencyConfig) {
                    $dependencies[] = [
                        'metric_name' => $metricName,
                        'cell_id' => $dependencyCellId,
                        'config_id' => $dependencyConfig->id,
                        'source_sheet' => $dependencyConfig->source_sheet,
                        'source_column' => $dependencyConfig->source_metric_column,
                        'aggregation' => $dependencyConfig->aggregation_type
                    ];
                }
            }
        }

        return $dependencies;
    }

    /**
     * Validate and debug col_index mappings for dependencies.
     *
     * @param int $reportId
     * @return array
     */
    public function validateColIndexMappings(int $reportId): array
    {
        $configs = EcomReportCellConfig::forReport($reportId)->get();
        $validation = [
            'total_configs' => $configs->count(),
            'missing_col_index' => 0,
            'valid_col_index' => 0,
            'issues' => []
        ];

        foreach ($configs as $config) {
            if ($config->col_index === null) {
                $validation['missing_col_index']++;
                $validation['issues'][] = [
                    'cell_id' => $config->cell_identifier,
                    'issue' => 'Missing col_index',
                    'sheet' => $config->source_sheet,
                    'column' => $config->source_metric_column
                ];
            } else {
                $validation['valid_col_index']++;
            }
        }

        Log::info("Col_index validation completed", $validation);
        return $validation;
    }

    /**
     * Save or update a cell configuration (compatible with old API).
     *
     * @param int $reportId
     * @param string $cellIdentifier
     * @param array $config
     * @return array
     */
    public function saveCellConfiguration(int $reportId, array $config): array
    {
        try {
            DB::beginTransaction();

            // Validate the report exists
            $report = EcomReport::findOrFail($reportId);

            // Find existing config or create new one
            $cellConfig = EcomReportCellConfig::where('ecom_report_id', $reportId)
                ->where('table_id', $config['table_id'])
                ->where('cell_identifier', $config['cell_identifier'])
                ->first();

            if (!$cellConfig) {
                $cellConfig = new EcomReportCellConfig();
                $cellConfig->ecom_report_id = $reportId;
                $cellConfig->cell_identifier = $config['cell_identifier'];
                $cellConfig->created_by = auth()->id() ?? 1;
            }

            // Update the configuration
            $cellConfig->table_id = $config['table_id'] ?? null;
            $cellConfig->row_index = $config['row_index'] ?? null;
            $cellConfig->col_index = $config['col_index'] ?? null;
            $cellConfig->source_sheet = $config['source_sheet'] ?? null;
            $cellConfig->source_metric_column = $config['source_metric_column'] ?? null;
            $cellConfig->value_type = $config['value_type'] ?? CellConfigConstants::VALUE_TYPE_NUMBER;

            // Map display format with validation
            $displayFormat = $config['display_format'] ?? $config['displayFormat'] ?? $config['numberFormat'] ?? 'general';
            $cellConfig->display_format = $this->mapToValidDisplayFormat($displayFormat);

            $cellConfig->aggregation_type = $config['aggregation'] ?? CellConfigConstants::AGGREGATION_DIRECT;
            $cellConfig->use_custom_formula = $config['use_custom_formula'] ?? false;
            $cellConfig->description = $config['description'] ?? null;
            $cellConfig->status = $config['status'] ?? CellConfigConstants::STATUS_ACTIVE;
            $cellConfig->updated_by = auth()->id() ?? 1;

            // Generate source_identifier (handle newlines and special characters)
            if ($cellConfig->source_sheet && $cellConfig->source_metric_column) {
                $cleanSheet = str_replace(["\n", "\r", "\t"], ' ', $cellConfig->source_sheet);
                $cleanColumn = str_replace(["\n", "\r", "\t"], ' ', $cellConfig->source_metric_column);
                $cellConfig->source_identifier = Str::slug($cleanSheet) . '_' . Str::slug($cleanColumn);
            }

            // Handle formula and auto-create dependency configs
            if ($cellConfig->use_custom_formula && isset($config['formula'])) {
                $cellConfig->formula_display = $config['formula_display'] ?? $this->buildFormulaDisplay($config['formula']);

                // Auto-create cell configurations for formula dependencies
                $newFormula = $this->createFormulaDependencyConfigs($reportId, $config['formula'], $config['cell_identifier'], $config['source_sheet'], $config['table_id']);
                $cellConfig->formula = $newFormula;
            } else {
                $cellConfig->formula = null;
                $cellConfig->formula_display = null;
            }

            $cellConfig->save();

            DB::commit();

            Log::info("Cell configuration saved successfully", [
                'report_id' => $reportId,
                'cell_identifier' => $config['cell_identifier'],
                'config_id' => $cellConfig->id
            ]);

            return [
                'success' => true,
                'message' => 'Cell configuration saved successfully.',
                'data' => $this->getCellConfiguration($reportId, $config['cell_identifier'])
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to save cell configuration", [
                'report_id' => $reportId,
                'cell_identifier' => $config['cell_identifier'],
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to save cell configuration: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Get a specific cell configuration.
     *
     * @param int $reportId
     * @param string $cellIdentifier
     * @return array|null
     */
    public function getCellConfiguration(int $reportId, string $cellIdentifier): ?array
    {
        $config = EcomReportCellConfig::forReport($reportId)
            ->forCell($cellIdentifier)
            ->active()
            ->first();

        if (!$config) {
            return null;
        }

        return [
            'id' => $config->id,
            'tableId' => $config->table_id,
            'cellIdentifier' => $config->cell_identifier,
            'rowIndex' => $config->row_index,
            'colIndex' => $config->col_index,
            'sourceSheet' => $config->source_sheet,
            'sourceMetricColumn' => $config->source_metric_column,
            'sourceIdentifier' => $config->source_identifier,
            'valueType' => $config->value_type,
            'displayFormat' => $config->display_format,
            'aggregation' => $config->aggregation_type,
            'useCustomFormula' => $config->use_custom_formula,
            'formula' => $config->formula ?? [],
            'formulaDisplay' => $config->formula_display,
            'description' => $config->description,
        ];
    }

    /**
     * Delete a cell configuration.
     *
     * @param int $reportId
     * @param string $cellIdentifier
     * @return array
     */
    public function deleteCellConfiguration(int $reportId, string $cellIdentifier): array
    {
        try {
            $config = EcomReportCellConfig::forReport($reportId)
                ->forCell($cellIdentifier)
                ->first();

            if (!$config) {
                return [
                    'success' => false,
                    'message' => 'Cell configuration not found.',
                ];
            }

            $config->delete();

            Log::info("Cell configuration deleted successfully", [
                'report_id' => $reportId,
                'cell_identifier' => $cellIdentifier,
                'config_id' => $config->id
            ]);

            return [
                'success' => true,
                'message' => 'Cell configuration deleted successfully.',
            ];
        } catch (\Exception $e) {
            Log::error("Failed to delete cell configuration", [
                'report_id' => $reportId,
                'cell_identifier' => $cellIdentifier,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to delete cell configuration: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Map input format to valid display format enum value.
     *
     * @param string $format
     * @return string
     */
    private function mapToValidDisplayFormat(string $format): string
    {
        // Check current database enum values first
        // Based on error, current enum seems to have 'default' but not 'general'
        $validFormats = [
            'default',  // Use 'default' as primary since it's in current enum
            'number_0',
            'number_2',
            'percent_0',
            'percent_2',
            'currency_vnd',
            'currency_usd',
            'short_number_k',
            'short_number_m',
            'short_number_b'
        ];

        // Direct mapping for exact matches
        if (in_array($format, $validFormats)) {
            return $format;
        }

        // Legacy mapping for old format names
        $legacyMapping = [
            'general' => 'default',  // Map 'general' to 'default' for current enum
            'percentage' => 'percent_0',
            'percent' => 'percent_0',
            'number' => 'number_0',
            'currency' => 'currency_vnd',
            'short_k' => 'short_number_k',
            'short_m' => 'short_number_m',
            'short_b' => 'short_number_b',
        ];

        if (isset($legacyMapping[$format])) {
            return $legacyMapping[$format];
        }

        // Default fallback - use 'default' since it exists in current enum
        Log::warning("Unknown display format: {$format}, using 'default' as fallback");
        return 'default';
    }

    /**
     * Create cell configurations for formula dependencies.
     *
     * @param int $reportId
     * @param array $formula
     * @param string $parentCellId
     * @return array
     */
    private function createFormulaDependencyConfigs(int $reportId, array $formula, string $parentCellId, string $sourceSheet, string $tableId = 'overall')
    {
        if (empty($formula)) {
            return;
        }

        $newFormula = $formula;

        Log::info("Creating dependency configs for formula", [
            'parent_cell' => $parentCellId,
            'formula_elements' => count($formula)
        ]);

        foreach ($formula as $index => $element) {
            if (!is_array($element) || !isset($element['type']) || $element['type'] !== 'metric') {
                continue; // Skip non-metric elements (operators, numbers)
            }

            $metricName = $element['value'];
            
            $dependencyCellId = $this->generateDependencyCellId($parentCellId, $metricName);

            // Check if dependency config already exists
            $existingConfig = EcomReportCellConfig::where('ecom_report_id', $reportId)
                ->where('table_id', $tableId)
                ->where('source_sheet', $sourceSheet)
                ->where('source_metric_column', $metricName)
                ->first();

            if ($existingConfig) {
                $newFormula[$index]['cell_identifier'] = $existingConfig->cell_identifier;
                Log::debug("Dependency config already exists: {$dependencyCellId}");
                continue;
            }

            // Create dependency configuration
            $dependencyConfig = $this->createDependencyConfig($reportId, $tableId, $dependencyCellId, $metricName, $sourceSheet);
            $newFormula[$index]['cell_identifier'] = $dependencyCellId;

            if ($dependencyConfig) {
                Log::info("Created dependency config", [
                    'cell_id' => $dependencyCellId,
                    'metric' => $metricName,
                    'config_id' => $dependencyConfig->id
                ]);
            }
        }

        return $newFormula;
    }

    /**
     * Generate cell ID for dependency metric.
     *
     * @param string $channel
     * @param string $category
     * @param string $metricName
     * @return string
     */
    private function generateDependencyCellId(string $parentCellId, string $metricName): string
    {
        // Convert metric name to cell ID format
        $metricName = Str::camel(Str::slug(strtolower($metricName), '-'));
        return $parentCellId . '_' . $metricName;
    }

    /**
     * Create dependency configuration for a metric.
     *
     * @param int $reportId
     * @param string $cellId
     * @param string $metricName
     * @param string $channel
     * @param string $category
     * @return EcomReportCellConfig|null
     */
    private function createDependencyConfig(int $reportId, string $tableId, string $cellId, string $metricName, string $sourceSheet): ?EcomReportCellConfig
    {
        try {
            // Map metric name to sheet and column with col_index
            $sheetMapping = $this->getSheetMappingForMetric($sourceSheet, $metricName);

            $dependencyConfig = new EcomReportCellConfig();
            $dependencyConfig->ecom_report_id = $reportId;
            $dependencyConfig->cell_identifier = $cellId;
            $dependencyConfig->table_id = $tableId; // Default table
            $dependencyConfig->row_index = null; // Will be determined during processing
            $dependencyConfig->col_index = $sheetMapping['col_index']; // ⭐ KEY FIX: Set col_index
            $dependencyConfig->source_sheet = $sheetMapping['sheet'];
            $dependencyConfig->source_metric_column = $sheetMapping['column'];
            $dependencyConfig->value_type = CellConfigConstants::VALUE_TYPE_NUMBER;
            $dependencyConfig->display_format = 'default';
            $dependencyConfig->aggregation_type = $sheetMapping['aggregation'];
            $dependencyConfig->use_custom_formula = false;
            $dependencyConfig->status = CellConfigConstants::STATUS_ACTIVE;
            $dependencyConfig->created_by = auth()->id() ?? 1;
            $dependencyConfig->updated_by = auth()->id() ?? 1;

            // Generate source_identifier
            if ($dependencyConfig->source_sheet && $dependencyConfig->source_metric_column) {
                $cleanSheet = str_replace(["\n", "\r", "\t"], ' ', $dependencyConfig->source_sheet);
                $cleanColumn = str_replace(["\n", "\r", "\t"], ' ', $dependencyConfig->source_metric_column);
                $dependencyConfig->source_identifier = Str::slug($cleanSheet) . '_' . Str::slug($cleanColumn);
            }

            $dependencyConfig->save();

            Log::info("Created dependency config with col_index", [
                'cell_id' => $cellId,
                'metric' => $metricName,
                'col_index' => $sheetMapping['col_index'],
                'sheet' => $sheetMapping['sheet'],
                'column' => $sheetMapping['column'],
                'config_id' => $dependencyConfig->id
            ]);

            return $dependencyConfig;
        } catch (\Exception $e) {
            Log::error("Failed to create dependency config", [
                'cell_id' => $cellId,
                'metric' => $metricName,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get sheet mapping for a specific metric by fetching real data from Google Sheets API.
     *
     * @param string $sourceSheet
     * @param string $metricName
     * @param string $channel
     * @return array
     */
    private function getSheetMappingForMetric(string $sourceSheet, string $metricName): array
    {
        // Default mapping
        $defaultMapping = [
            'sheet' => $sourceSheet,
            'column' => $metricName,
            'col_index' => null,
            'aggregation' => CellConfigConstants::AGGREGATION_SUM
        ];

        try {
            // Get the report to access sheet_id
            $report = EcomReport::where('id', request()->input('report_id', 1))->first();
            if (!$report) {
                Log::warning("Report not found, using default mapping for metric: {$metricName}");
                return $defaultMapping;
            }

            // Get headers from Google Sheets API
            $headers = $this->getSheetHeadersFromAPI($report->sheet_id, $sourceSheet);

            if (empty($headers)) {
                Log::warning("No headers found for sheet: {$sourceSheet}, using default mapping");
                $mapping = $defaultMapping;
                $mapping['sheet'] = $sourceSheet;
                return $mapping;
            }

            // Find the column index by matching metric name with headers
            $colIndex = $this->findColumnIndexInHeaders($metricName, $headers);
            $actualColumn = $colIndex !== null ? $headers[$colIndex] : $metricName;

            // Determine aggregation type based on metric name
            $aggregationType = 'sum';

            $mapping = [
                'sheet' => $sourceSheet,
                'column' => $actualColumn,
                'col_index' => $colIndex,
                'aggregation' => $aggregationType
            ];

            return $mapping;
        } catch (\Exception $e) {
            Log::error("Error getting sheet mapping from API for metric {$metricName}: " . $e->getMessage());

            return $mapping;
        }
    }

    /**
     * Get headers from Google Sheets API.
     *
     * @param string $spreadsheetId
     * @param string $sheetName
     * @return array
     */
    private function getSheetHeadersFromAPI(string $spreadsheetId, string $sheetName): array
    {
        try {
            $client = new Client();
            $client->setAuthConfig(storage_path('app/service_account.json'));
            $client->addScope(Sheets::SPREADSHEETS_READONLY);
            $service = new Sheets($client);

            // Get headers from row 1 and row 2 (some sheets have multi-row headers)
            $range = $sheetName . '!1:1';
            $response = $service->spreadsheets_values->get($spreadsheetId, $range);
            $values = $response->getValues();

            if (empty($values)) {
                return [];
            }

            // Combine headers from both rows if needed
            $headers = [];
            $row1 = $values[0] ?? [];
            $row2 = $values[1] ?? [];

            for ($i = 0; $i < max(count($row1), count($row2)); $i++) {
                $header1 = $row1[$i] ?? '';
                $header2 = $row2[$i] ?? '';

                // Combine headers with newline if both exist
                if (!empty($header1) && !empty($header2) && $header1 !== $header2) {
                    $headers[$i] = $header1 . "\n" . $header2;
                } else {
                    $headers[$i] = !empty($header1) ? $header1 : $header2;
                }
            }

            Log::debug("Headers found for sheet {$sheetName}", [
                'header_count' => count($headers),
                'sample_headers' => array_slice($headers, 0, 5)
            ]);

            return $headers;
        } catch (\Exception $e) {
            Log::error("Error getting headers for sheet {$sheetName}: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Find column index in headers by matching metric name.
     *
     * @param string $metricName
     * @param array $headers
     * @return int|null
     */
    private function findColumnIndexInHeaders(string $metricName, array $headers): ?int
    {
        // First pass: Try exact matches only
        foreach ($headers as $index => $header) {
            if (trim($header) === trim($metricName)) {
                return $index;
            }
        }

        // Second pass: Try case-insensitive matches if no exact match found
        foreach ($headers as $index => $header) {
            if (strcasecmp(trim($header), trim($metricName)) === 0) {
                return $index;
            }
        }

        // Third pass: Try partial matches only if no exact/case-insensitive match found
        // Only for headers with line breaks or complex formatting
        foreach ($headers as $index => $header) {
            // Only do partial matching if header contains newlines or target is significantly different
            if (strpos($header, "\n") !== false || strpos($metricName, "\n") !== false) {
                if (stripos($header, $metricName) !== false || stripos($metricName, $header) !== false) {
                    return $index;
                }
            }
        }

        return null;
    }

    /**
     * Build formula display from formula array.
     *
     * @param array $formula
     * @return string
     */
    private function buildFormulaDisplay(array $formula): string
    {
        if (empty($formula)) {
            return '';
        }

        return collect($formula)
            ->map(function ($item) {
                if (is_array($item) && isset($item['type'])) {
                    if ($item['type'] === 'metric') {
                        return "[{$item['value']}]";
                    }
                    return $item['value'];
                }
                return $item;
            })
            ->join(' ');
    }

    /**
     * Get all cell configurations for a report
     */
    public function getCellConfigs(EcomReport $report): array
    {
        return $report->cellConfigs()->get()->toArray();
    }

    /**
     * Get cell configurations as associative array
     */
    public function getCellConfigsArray(EcomReport $report): array
    {
        return $report->getCellConfigsArray();
    }

    /**
     * Save or update a cell configuration
     */
    public function saveCellConfig(EcomReport $report, array $data): EcomReportCellConfig
    {
        $cellIdentifier = $data['cell_identifier'];

        // Check if configuration already exists
        $existingConfig = $report->cellConfigs()
            ->where('cell_identifier', $cellIdentifier)
            ->first();

        if ($existingConfig) {
            $existingConfig->update($data);
            $this->clearCache($report->id);
            return $existingConfig->fresh();
        } else {
            $cellConfig = $report->cellConfigs()->create($data);
            $this->clearCache($report->id);
            return $cellConfig;
        }
    }

    /**
     * Delete a cell configuration
     */
    public function deleteCellConfig(EcomReport $report, string $cellIdentifier): bool
    {
        $cellConfig = $report->cellConfigs()
            ->where('cell_identifier', $cellIdentifier)
            ->first();

        if ($cellConfig) {
            $cellConfig->delete();
            $this->clearCache($report->id);
            return true;
        }

        return false;
    }

    /**
     * Bulk update cell configurations
     */
    public function bulkUpdateCellConfigs(EcomReport $report, array $configurations): array
    {
        $createdCount = 0;
        $updatedCount = 0;

        foreach ($configurations as $configData) {
            $cellIdentifier = $configData['cell_identifier'];

            $existingConfig = $report->cellConfigs()
                ->where('cell_identifier', $cellIdentifier)
                ->first();

            if ($existingConfig) {
                $existingConfig->update($configData);
                $updatedCount++;
            } else {
                $report->cellConfigs()->create($configData);
                $createdCount++;
            }
        }

        $this->clearCache($report->id);

        return [
            'created' => $createdCount,
            'updated' => $updatedCount
        ];
    }

    /**
     * Get cell configuration by identifier
     */
    public function getCellConfig(EcomReport $report, string $cellIdentifier): ?EcomReportCellConfig
    {
        return $report->getCellConfig($cellIdentifier);
    }

    /**
     * Validate cell configuration data
     */
    public function validateCellConfigData(array $data): array
    {
        $errors = [];

        // Required fields
        if (empty($data['cell_identifier'])) {
            $errors[] = 'Cell identifier is required';
        }

        if (empty($data['value_type'])) {
            $errors[] = 'Value type is required';
        } elseif (!in_array($data['value_type'], ['text', 'number'])) {
            $errors[] = 'Value type must be either "text" or "number"';
        }

        // Validate based on configuration type
        if (!empty($data['use_custom_formula']) && $data['use_custom_formula']) {
            // Custom formula validation
            if (empty($data['formula']) || !is_array($data['formula'])) {
                $errors[] = 'Formula is required when using custom formula';
            }
        } else {
            // Sheet-based configuration validation
            if (empty($data['source_sheet'])) {
                $errors[] = 'Source sheet is required when not using custom formula';
            }
            if (empty($data['source_metric_column'])) {
                $errors[] = 'Source metric column is required when not using custom formula';
            }
        }

        // Number format validation
        if (!empty($data['value_type']) && $data['value_type'] === 'number') {
            $validFormats = [
                'general',
                'currency_vnd',
                'currency_usd',
                'percentage',
                'short_number_k',
                'short_number_m',
                'short_number_b'
            ];
            if (!empty($data['number_format']) && !in_array($data['number_format'], $validFormats)) {
                $errors[] = 'Invalid number format';
            }
        }

        // Aggregation validation
        $validAggregations = ['sum', 'average', 'first', 'last', 'count', 'direct'];
        if (!empty($data['aggregation']) && !in_array($data['aggregation'], $validAggregations)) {
            $errors[] = 'Invalid aggregation type';
        }

        return $errors;
    }

    /**
     * Clear cache for a report
     */
    private function clearCache(int $reportId): void
    {
        $cacheKeys = [
            "ecom_report_cell_configs_{$reportId}",
            "ecom_report_cell_configs_array_{$reportId}"
        ];

        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }
    }

    /**
     * Get cell configuration statistics for a report
     */
    public function getCellConfigStats(EcomReport $report): array
    {
        $totalConfigs = $report->cellConfigs()->count();
        $customFormulaConfigs = $report->cellConfigs()->where('use_custom_formula', true)->count();
        $sheetConfigs = $report->cellConfigs()->where('use_custom_formula', false)->count();

        return [
            'total' => $totalConfigs,
            'custom_formula' => $customFormulaConfigs,
            'sheet_based' => $sheetConfigs,
            'configured_percentage' => $totalConfigs > 0 ? round(($totalConfigs / 100) * 100, 2) : 0
        ];
    }

    /**
     * Export cell configurations for a report
     */
    public function exportCellConfigs(EcomReport $report): array
    {
        $configs = $report->cellConfigs()->get();

        $exportData = [];
        foreach ($configs as $config) {
            $exportData[] = [
                'cell_identifier' => $config->cell_identifier,
                'table_id' => $config->table_id,
                'row_index' => $config->row_index,
                'col_index' => $config->col_index,
                'use_custom_formula' => $config->use_custom_formula,
                'source_sheet' => $config->source_sheet,
                'source_metric_column' => $config->source_metric_column,
                'value_type' => $config->value_type,
                'number_format' => $config->number_format,
                'aggregation' => $config->aggregation,
                'formula' => $config->formula,
                'description' => $config->description,
                'created_at' => $config->created_at,
                'updated_at' => $config->updated_at
            ];
        }

        return $exportData;
    }
}
