/* ==========================================================================
   E-commerce Report Styles
   ========================================================================== */

/* CSS Custom Properties for consistent theming */
:root {
    /* Colors */
    --primary-color: #7a121d;
    --primary-light: #F4C7C3;
    --secondary-color: #b71c1c;
    --accent-color: #0072f0;
    --text-primary: #333;
    --text-secondary: #666;
    --text-muted: #5f6368;
    --white: #fff;
    --background-light: #f5f5f5;
    --border-color: #ddd;
    --success-color: #1565c0;

    /* Typography */
    --font-family: 'Roboto', sans-serif;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    --font-size-2xl: 1.75rem;

    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 20px;
    --spacing-xl: 24px;
    --spacing-2xl: 60px;

    /* Border radius */
    --border-radius: 4px;
    --border-radius-lg: 10px;

    /* Shadows */
    --shadow-sm: rgba(0, 0, 0, 0.14) 0px 2px 2px 0px, rgba(0, 0, 0, 0.2) 0px 1px 1px 0px, rgba(0, 0, 0, 0.12) 0px 1px 5px 0px;
}

/* ==========================================================================
   Base Styles
   ========================================================================== */

body {
    font-family: var(--font-family);
    background-color: var(--background-light);
    margin: 0;
    padding: 0;
}

html {
    --nav-item-color: var(--text-muted);
    --active-nav-item-color: var(--accent-color);
}

/* ==========================================================================
   Navigation Styles
   ========================================================================== */

.navbar-menu {
    background-color: var(--white) !important;
    border-right: 0 !important;

    .nav-link {
        color: var(--text-muted) !important;

        &:hover {
            color: var(--nav-item-color) !important;
            background-color: #F6F6F6 !important;
        }
    }

    .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true] {
        color: var(--nav-item-color) !important;
    }

    .nav-item.active {
        font-weight: 600;

        .nav-link {
            color: var(--active-nav-item-color) !important;
            background-color: #CCDDF8;
        }
    }
}

/* ==========================================================================
   Report Container
   ========================================================================== */

.report-container {
    background-color: var(--white);
    width: calc(100vw - 250px);
    overflow-x: scroll;
}

/* ==========================================================================
   Report Header
   ========================================================================== */

.report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--white);
    border-bottom: 1px solid var(--border-color);

    .reportrange-container {
        padding-bottom: var(--spacing-xs);
        border-bottom: 1px solid var(--primary-color);
    }

    .overview-reportrange {
        border: 1px solid var(--primary-color);
        padding: var(--spacing-sm) var(--spacing-xs) var(--spacing-sm) 48px;
        margin-right: 5px;
        border-radius: var(--border-radius);
        color: var(--primary-color);
        box-shadow: var(--shadow-sm);
    }

    .title {
        border: 1px solid var(--primary-color);
        background: transparent;
        border-radius: 0;
        padding: var(--spacing-sm);
        font-size: var(--font-size-2xl);
        color: var(--primary-color);
    }

    img {
        width: auto;
        height: 45px;
        padding-bottom: var(--spacing-xs);
        border-bottom: 1px solid var(--primary-color);
    }
}

/* Date Range Selector */
.report-header .date-range-selector {
    background: none;
    border: 1px solid #ccc;
    padding: 5px var(--spacing-sm);
    cursor: pointer;
    border-radius: var(--border-radius);

    .header {
        .date-range {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
        }

        .title {
            font-size: 1.25rem;
            font-weight: bold;
            color: var(--text-primary);
        }

        .logo {
            font-size: var(--font-size-lg);
            font-weight: bold;
            color: #8B1E3F;
        }
    }

    .container {
        padding: var(--spacing-lg);
    }
}

/* ==========================================================================
   Report Content
   ========================================================================== */

.report-content {

    /* Common table styles */
    .table-container,
    .revenue-table-container {
        margin: var(--spacing-lg);

        .table {

            thead,
            th,
            tr,
            td {
                border: none;
                border-bottom-width: 0 !important;
                padding: 1px;
                margin: 2px;
            }

            td {
                height: 50px;
                padding: 5px;
            }
        }
    }

    /* Main table container */
    .table-container {
        .table {

            /* Group styling */
            td .group {
                color: var(--primary-color);
                font-size: var(--font-size-xl);
                border: 1px solid var(--primary-color);
                border-radius: var(--border-radius-lg);
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                font-weight: bold;
                padding: var(--spacing-md) var(--spacing-xl);
            }

            /* Header row styling */
            .header-row-parent td>div,
            .header-row td>div {
                height: 70px;
                background: var(--primary-color);
                color: var(--white);
                font-weight: 600;
                font-size: var(--font-size-sm);
                display: flex;
                justify-content: center;
                align-items: center;
                text-align: center;
                padding: 0 var(--spacing-xs);
                border-radius: var(--border-radius-lg);
            }

            /* Channel column styling */
            .channel-col>div {
                color: var(--white);
                background: var(--secondary-color);
                border-radius: var(--border-radius-lg);
                height: 55px;
                font-weight: 600;
                font-size: var(--font-size-sm);
                text-align: center;
                align-content: center;
                height: 100%;
            }

            /* Timeline styling */
            td div.tb-timeline {
                height: 100%;
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                text-align: center;
                border: 1px solid var(--success-color);
                background: var(--white);
                border-radius: var(--border-radius-lg);
                font-size: var(--font-size-lg);
            }

            /* Value styling */
            tr td .actual-value,
            tr td .achievement-value {
                border: 1px solid var(--success-color);
                background: var(--white);
                border-radius: var(--border-radius-lg);
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                text-align: center;
                font-size: var(--font-size-lg);
                margin: 0 var(--spacing-xs);
            }
        }
    }

    /* Revenue table container */
    .revenue-table-container {
        display: flex;
        justify-content: center;

        .table {
            width: max-content;

            td .group {
                color: var(--primary-color);
                font-size: var(--font-size-sm);
                font-weight: 700;
                border: 1px solid var(--primary-color);
                border-radius: var(--border-radius-lg);
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: var(--spacing-md) var(--spacing-xl);
            }

            .header-row-parent td>div,
            .header-row td>div {
                height: 51px;
                background: var(--primary-color);
                color: var(--white);
                font-weight: 600;
                font-size: var(--font-size-sm);
                display: flex;
                justify-content: center;
                align-items: center;
                text-align: center;
                padding: 0 var(--spacing-sm);
                border-radius: var(--border-radius-lg);
            }

            .channel-col>div {
                height: 55px;
                background: var(--secondary-color);
                color: var(--white);
                font-weight: 600;
                font-size: var(--font-size-sm);
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 0 var(--spacing-sm);
                border-radius: var(--border-radius-lg);
            }

            td.tb-timeline {
                background-color: var(--primary-light);
                color: var(--text-primary);
                text-align: center;
                min-width: 74px;
                width: 74px;
                font-size: var(--font-size-lg);
                border: 1px solid var(--accent-color);
                border-radius: 0;
                border-bottom-width: 1px !important;
            }

            tr td:has(.plan-value) {
                font-size: var(--font-size-base);
                text-align: center;
                background: var(--primary-light);
                border: 2px solid var(--accent-color);
                border-radius: 0;
                border-bottom-width: 2px !important;
                border-right-width: 0 !important;
                border-left-width: 0 !important;
            }

            tr>td:has(.plan-value) {
                border: 2px solid var(--accent-color);
                border-radius: 0;
                border-right-width: 2px !important;
            }

            tr td .actual-value,
            tr td .achievement-value {
                border: 1px solid var(--success-color);
                background: var(--white);
                border-radius: var(--border-radius-lg);
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                text-align: center;
                font-size: var(--font-size-lg);
                margin: 0 var(--spacing-xs);
            }
        }
    }

    /* Common value styles */
    .plan-value,
    .actual-value,
    .achievement-value {
        height: 100%;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        border: 1px solid var(--success-color);
        background: var(--white);
        border-radius: var(--border-radius-lg);
        font-size: var(--font-size-lg);
    }

    /* Column styles */
    .platform-col,
    .timeline-col,
    .format-col,
    .buying-unit-col {
        color: var(--primary-color);
        font-size: var(--font-size-sm);
        border: 1px solid var(--primary-color);
        border-radius: var(--border-radius-lg);
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        background-color: var(--primary-light);
        border-color: #FFA07A;
    }

    /* Timeline styles */
    .timeline {
        height: 50px;

        .timeline-label {
            background: var(--primary-color);
            color: var(--white);
            font-weight: 600;
            font-size: var(--font-size-sm);
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 0 var(--spacing-xs);
            border-radius: var(--border-radius-lg);
        }

        .timeline-value {
            height: 100%;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            border: 1px solid var(--success-color);
            background: var(--white);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-lg);
        }
    }

    /* Group section */
    .group-section {
        color: var(--primary-color);
        font-size: var(--font-size-xl);
        border: 1px solid var(--primary-color);
        border-radius: var(--border-radius-lg);
        width: 200px !important;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
        padding: var(--spacing-md) var(--spacing-xl);
        background-color: var(--primary-light);
    }

    /* Performance table */
    #performance-table {
        td {
            text-align: right;
        }

        thead tr {
            background-color: #E0E0E0;
        }
    }

    /* Chart container */
    .chart-container .header-group {
        background-color: #8B0000;
        color: var(--white);
        padding: var(--spacing-sm);
        margin: calc(-1 * var(--spacing-lg)) calc(-1 * var(--spacing-lg)) var(--spacing-lg) calc(-1 * var(--spacing-lg));
        width: 200px;
    }
}

/* ==========================================================================
   Report Comment
   ========================================================================== */

.report-comment {
    padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-2xl) var(--spacing-lg);

    .weekly-comment-header {
        background: var(--primary-color);
        border-radius: var(--border-radius-lg);
        width: max-content;
        padding: var(--spacing-xl);
        color: var(--white);
        font-size: var(--font-size-sm);
        line-height: 5px;
    }
}