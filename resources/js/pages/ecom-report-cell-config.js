let cellConfigModalInstance = null; // Bootstrap Modal instance
let cellSheetNameSelectElem,
    cellMetricColumnSelectElem,
    cellValueTypeSelectElem,
    cellNumberOptionsContainerElem,
    cellDisplayFormatSelectElem,
    cellAggregationTypeSelectElem,
    editingCellIdentifierInputElem;
let formulaBuilderElementsRef; // To store references to formula builder elements
let cellConfigurationsRef; // Reference to the main cellConfigurations object
let activeEditingCellElementRef; // Reference to the TD element being edited

function getCellIdentifier(cellElement) {
    if (cellElement.dataset.cellId) {
        return cellElement.dataset.cellId;
    }
    const innerValueDiv = cellElement.querySelector(
        ".plan-value, .actual-value, .achievement-value"
    );
    let cellId = "cell-" + Array.from(cellElement.classList).join("-");
    if (innerValueDiv && innerValueDiv.classList.length > 0) {
        const specificClass = Array.from(innerValueDiv.classList).find(
            (cls) =>
                cls.includes("-plan-") ||
                cls.includes("-actual-") ||
                cls.includes("-achievement-")
        );
        if (specificClass) cellId = specificClass;
    }
    if (cellId === "cell-configurable-cell") {
        const table = cellElement.closest("table");
        const tableId = table ? table.id || "unknownTable" : "unknownTable";
        cellId = `${tableId}-r${cellElement.parentElement.rowIndex}-c${cellElement.cellIndex}`;
    }
    return cellId;
}

export function initializeCellConfigModal(
    elements,
    formulaBuilderElems,
    globalCellConfigs,
    saveCallback
) {
    cellConfigModalInstance = elements.modalElement
        ? new bootstrap.Modal(elements.modalElement)
        : null;
    if (!cellConfigModalInstance) {
        console.error(
            "Cell config modal element not found or Bootstrap not initialized."
        );
        return;
    }

    cellSheetNameSelectElem = elements.sheetSelect;
    cellMetricColumnSelectElem = elements.metricColumnSelect;
    cellValueTypeSelectElem = elements.valueTypeSelect;
    cellNumberOptionsContainerElem = elements.numberOptionsContainer;
    cellDisplayFormatSelectElem = elements.displayFormatSelect;
    cellAggregationTypeSelectElem = elements.aggregationTypeSelect;
    editingCellIdentifierInputElem = elements.identifierInput;
    formulaBuilderElementsRef = formulaBuilderElems;
    cellConfigurationsRef = globalCellConfigs;

    if (cellSheetNameSelectElem) {
        // Use jQuery for Select2 change event
        $(cellSheetNameSelectElem).on("change", function () {
            const sheetName = this.value;
            console.log("Sheet name changed to:", sheetName);

            populateMetricColumnsForModal(
                sheetName,
                null,
                cellMetricColumnSelectElem,
                false
            );

            // Reset subsequent fields if sheet changes and not in custom formula mode
            if (!formulaBuilderElementsRef.enableCustomFormulaSwitch.checked) {
                // Reset using Select2 methods
                $(cellMetricColumnSelectElem).val("").trigger("change");
                $(cellValueTypeSelectElem).val("number").trigger("change");

                if (cellNumberOptionsContainerElem)
                    cellNumberOptionsContainerElem.style.display = "block";

                $(cellDisplayFormatSelectElem).val("default").trigger("change");
                $(cellAggregationTypeSelectElem).val("sum").trigger("change");
            }

            const cellId = editingCellIdentifierInputElem.value;
            if (cellId && cellConfigurationsRef[cellId]) {
                cellConfigurationsRef[cellId].sourceSheet = this.value;
                cellConfigurationsRef[cellId].sourceMetricColumn = "";
                if (
                    formulaBuilderElementsRef.enableCustomFormulaSwitch
                        .checked &&
                    formulaBuilderElementsRef.populateCellFormulaOperands
                ) {
                    formulaBuilderElementsRef.populateCellFormulaOperands(
                        sheetName,
                        cellConfigurationsRef,
                        cellId
                    );
                }
            }
        });
    }

    if (cellValueTypeSelectElem) {
        // Use jQuery for Select2 compatibility
        $(cellValueTypeSelectElem).on("change", function () {
            const value = $(this).val() || this.value;
            if (cellNumberOptionsContainerElem) {
                cellNumberOptionsContainerElem.style.display =
                    value === "number" ? "block" : "none";
            }
            const cellId = editingCellIdentifierInputElem.value;
            if (cellId && cellConfigurationsRef[cellId]) {
                cellConfigurationsRef[cellId].valueType = value;
            }
        });
    }

    document
        .querySelectorAll(".report-content .configurable-cell")
        .forEach((cell) => {
            cell.addEventListener("click", function () {
                activeEditingCellElementRef = this;

                var columnIndex = activeEditingCellElementRef.cellIndex;
                var tbody = activeEditingCellElementRef.closest("tbody"); // Find the closest tbody
                if (tbody) {
                    var headerRow = tbody.querySelector("tr.header-row");
                    let cellTitle =
                        activeEditingCellElementRef.dataset.cellName;
                    // Find the header row within this tbody
                    if (headerRow) {
                        var headerCell = headerRow.cells[columnIndex]; // Get the cell at the same column index
                        var modalTitleSpan = document.querySelector(
                            "#cellConfigModalLabel .cell-name"
                        );
                        if (modalTitleSpan && (cellTitle || headerCell))
                            modalTitleSpan.textContent =
                                cellTitle || headerCell.textContent.trim();
                    }
                }

                const cellId = getCellIdentifier(this);
                editingCellIdentifierInputElem.value = cellId;

                if (!cellConfigurationsRef[cellId]) {
                    cellConfigurationsRef[cellId] = {
                        sourceSheet: "",
                        sourceMetricColumn: "",
                        valueType: "number",
                        displayFormat: "default",
                        aggregation: "sum",
                        useCustomFormula: false,
                        formula: [],
                    };
                }

                const config = cellConfigurationsRef[cellId];

                formulaBuilderElementsRef.enableCustomFormulaSwitch.checked =
                    config.useCustomFormula || false;
                formulaBuilderElementsRef.customFormulaBuilderDiv.style.display =
                    formulaBuilderElementsRef.enableCustomFormulaSwitch.checked
                        ? "block"
                        : "none";
                // if (cellSheetNameSelectElem)
                //     cellSheetNameSelectElem.disabled =
                //         formulaBuilderElementsRef.enableCustomFormulaSwitch.checked;
                if (cellMetricColumnSelectElem)
                    cellMetricColumnSelectElem.disabled =
                        formulaBuilderElementsRef.enableCustomFormulaSwitch.checked;

                // Set values using Select2 methods if available
                if (cellSheetNameSelectElem) {
                    if ($(cellSheetNameSelectElem).data("select2")) {
                        $(cellSheetNameSelectElem)
                            .val(config.sourceSheet || "")
                            .trigger("change.select2");
                    } else {
                        cellSheetNameSelectElem.value =
                            config.sourceSheet || "";
                    }
                }

                populateMetricColumnsForModal(
                    config.sourceSheet,
                    config.sourceMetricColumn,
                    cellMetricColumnSelectElem
                );

                if (cellValueTypeSelectElem) {
                    if ($(cellValueTypeSelectElem).data("select2")) {
                        $(cellValueTypeSelectElem)
                            .val(config.valueType || "number")
                            .trigger("change.select2");
                    } else {
                        cellValueTypeSelectElem.value =
                            config.valueType || "number";
                    }
                }

                if (cellNumberOptionsContainerElem)
                    cellNumberOptionsContainerElem.style.display =
                        cellValueTypeSelectElem &&
                        ($(cellValueTypeSelectElem).val() ||
                            cellValueTypeSelectElem.value) === "number"
                            ? "block"
                            : "none";

                if (cellDisplayFormatSelectElem) {
                    if ($(cellDisplayFormatSelectElem).data("select2")) {
                        $(cellDisplayFormatSelectElem)
                            .val(config.displayFormat || "default")
                            .trigger("change.select2");
                    } else {
                        cellDisplayFormatSelectElem.value =
                            config.displayFormat || "default";
                    }
                }

                if (cellAggregationTypeSelectElem) {
                    if ($(cellAggregationTypeSelectElem).data("select2")) {
                        $(cellAggregationTypeSelectElem)
                            .val(config.aggregation || "sum")
                            .trigger("change.select2");
                    } else {
                        cellAggregationTypeSelectElem.value =
                            config.aggregation || "sum";
                    }
                }

                if (formulaBuilderElementsRef.setCurrentFormula) {
                    formulaBuilderElementsRef.setCurrentFormula(
                        config.formula ? [...config.formula] : []
                    );
                }
                if (formulaBuilderElementsRef.renderCellFormulaDisplay) {
                    formulaBuilderElementsRef.renderCellFormulaDisplay();
                }
                if (formulaBuilderElementsRef.populateCellFormulaOperands) {
                    let sheetNameToUseForOperands =
                        cellSheetNameSelectElem.value || config.sourceSheet;
                    formulaBuilderElementsRef.populateCellFormulaOperands(
                        sheetNameToUseForOperands,
                        cellConfigurationsRef,
                        cellId
                    );
                }

                fetchSheetNamesForModal(
                    cellSheetNameSelectElem,
                    config.sourceSheet
                );
                cellConfigModalInstance.show();
            });
        });

    if (elements.saveButton) {
        elements.saveButton.addEventListener("click", function () {
            if (saveCallback && typeof saveCallback === "function") {
                saveCallback(
                    activeEditingCellElementRef,
                    cellConfigurationsRef
                );
            }
        });
    }
}

export function fetchSheetNamesForModal(selectElement, currentValue = null) {
    if (!selectElement) return;

    // Use sheetId from window.sheetId since sheet names are sheet-specific, not report-specific
    const apiGetSheetNamesUrl = window.apiGetSheetNamesUrl || "";

    axios
        .get(apiGetSheetNamesUrl)
        .then((response) => {
            selectElement.innerHTML =
                '<option value="">-- Select Sheet --</option>';
            (response.data.sheet_names || []).forEach((name) => {
                const option = document.createElement("option");
                option.value = name;
                option.textContent = name;
                selectElement.appendChild(option);
            });
            if (
                currentValue &&
                response.data.sheet_names.includes(currentValue)
            ) {
                // Set value using Select2 method if available, otherwise use vanilla JS
                if ($(selectElement).data("select2")) {
                    $(selectElement)
                        .val(currentValue)
                        .trigger("change.select2");
                } else {
                    selectElement.value = currentValue;
                }
            }
        })
        .catch((error) => {
            console.error("Error fetching sheet names for modal:", error);
            selectElement.innerHTML =
                '<option value="">Error loading sheets</option>';
        });
}

export function populateMetricColumnsForModal(
    sheetName,
    selectedColumnIndex = null,
    columnSelectElement,
    isForFormulaBuilder = false
) {
    if (!columnSelectElement) return;
    columnSelectElement.innerHTML =
        '<option value="">-- Select Column --</option>';
    columnSelectElement.disabled = !sheetName;

    if (sheetName) {
        // Use sheetId from window.sheetId since column data is sheet-specific
        const apiPostGetSheetColumnsUrl = window.apiPostGetSheetColumnsUrl || "";

        axios
            .post(apiPostGetSheetColumnsUrl, {
                sheet_name: sheetName,
            })
            .then((response) => {
                if (isForFormulaBuilder) {
                    const numberOption = document.createElement("option");
                    numberOption.value = "number:custom"; // Special value for custom number
                    numberOption.textContent = "Custom Number...";
                    columnSelectElement.appendChild(numberOption);
                }

                (response.data.columns || []).forEach((column) => {
                    const option = document.createElement("option");
                    option.value = column.name;
                    option.textContent = `${column.name} (Index ${column.index})`;
                    if (
                        selectedColumnIndex !== null &&
                        String(column.index) === String(selectedColumnIndex)
                    ) {
                        option.selected = true;
                    }
                    columnSelectElement.appendChild(option);
                });
                columnSelectElement.disabled = false;

                // Update Select2 if it's initialized
                if ($(columnSelectElement).data("select2")) {
                    // Destroy and recreate Select2 to refresh options
                    $(columnSelectElement).select2("destroy");
                    $(columnSelectElement).select2({
                        width: "100%",
                        placeholder: "-- Select Column --",
                        allowClear: false,
                        dropdownParent: $("#cellConfigModal"),
                        minimumResultsForSearch: 3,
                    });
                }
            })
            .catch((error) => {
                console.error("Error fetching columns for modal:", error);
                columnSelectElement.innerHTML =
                    '<option value="">Error loading</option>';
            });
    } else if (isForFormulaBuilder) {
        // If no sheet name but it's for formula builder, still add "Custom Number"
        const numberOption = document.createElement("option");
        numberOption.value = "number:custom";
        numberOption.textContent = "Custom Number...";
        columnSelectElement.appendChild(numberOption);
        columnSelectElement.disabled = false; // Ensure it's enabled to select "Custom Number"
    }
}
