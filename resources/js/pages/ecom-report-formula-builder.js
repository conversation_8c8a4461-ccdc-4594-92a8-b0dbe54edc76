// resources/js/pages/ecom-report-formula-builder.js
import { populateMetricColumnsForModal } from "./ecom-report-cell-config.js";

let currentFormula = [];
let currentCellFormulaDisplayElement;
let cellFormulaOperandSelectElement;
let cellConfigurationsRef;

// Helper function to get column letter from index (0=A, 1=B, ...)
function getColumnLetter(colIndex) {
    let temp,
        letter = "";
    while (colIndex >= 0) {
        temp = colIndex % 26;
        letter = String.fromCharCode(temp + 65) + letter;
        colIndex = Math.floor(colIndex / 26) - 1;
    }
    return letter;
}

function renderCellFormulaDisplay() {
    if (!currentCellFormulaDisplayElement) return;
    currentCellFormulaDisplayElement.innerHTML = ""; // Clear previous display

    currentFormula.forEach((item, index) => {
        const tag = document.createElement("span");
        tag.className = "badge bg-secondary me-1 mb-1 p-2"; // Bootstrap styling for tags

        let displayText = "";
        if (item.type === "metric") {
            displayText = `[${item.value}]`;
        } else if (item.type === "number") {
            displayText = item.value.toString();
        } else if (item.type === "operator") {
            displayText = item.value;
        }
        tag.textContent = displayText;

        const removeBtn = document.createElement("button");
        removeBtn.type = "button";
        removeBtn.className = "btn-close btn-sm ms-1";
        removeBtn.setAttribute("aria-label", "Remove");
        removeBtn.style.fontSize = "0.6em";
        removeBtn.onclick = () => {
            currentFormula.splice(index, 1);
            renderCellFormulaDisplay();
        };
        tag.appendChild(removeBtn);
        currentCellFormulaDisplayElement.appendChild(tag);
    });
}

async function populateCellFormulaOperands(
    selectedSheetName,
    allCellConfigs,
    currentEditingCellId
) {
    if (!cellFormulaOperandSelectElement) return;
    cellFormulaOperandSelectElement.innerHTML =
        '<option value="">Loading operands...</option>'; // Placeholder

    // Populate with sheet columns and "Custom Number..."
    // The populateMetricColumnsForModal will handle the "Custom Number..." option when isForFormulaBuilder is true
    await populateMetricColumnsForModal(
        selectedSheetName,
        null,
        cellFormulaOperandSelectElement,
        true
    );

    // Add other configured cells as operands (excluding the current cell being edited)
    // if (allCellConfigs) {
    //     for (const cellId in allCellConfigs) {
    //         if (
    //             allCellConfigs.hasOwnProperty(cellId) &&
    //             cellId !== currentEditingCellId
    //         ) {
    //             const config = allCellConfigs[cellId];
    //             let cellDisplayName = cellId; // Default to cellId

    //             if (config.customName) {
    //                 cellDisplayName = config.customName;
    //             } else if (
    //                 config.sourceSheet &&
    //                 config.sourceMetricColumn !== "" &&
    //                 config.sourceMetricColumn !== null
    //             ) {
    //                 cellDisplayName = `${
    //                     config.sourceSheet
    //                 } - Col ${getColumnLetter(
    //                     parseInt(config.sourceMetricColumn)
    //                 )}`;
    //             } else if (
    //                 config.useCustomFormula &&
    //                 config.formula &&
    //                 config.formula.length > 0
    //             ) {
    //                 cellDisplayName = `Formula Cell (${cellId})`;
    //             }
    //             const option = document.createElement("option");
    //             option.value = `cell:${cellId}`; // Use a 'cell:' prefix for cell references
    //             option.textContent = `${cellDisplayName} (Ref: ${cellId})`;
    //             cellFormulaOperandSelectElement.appendChild(option);
    //         }
    //     }
    // }
}

export function initializeFormulaBuilder(elements, allCellConfigs) {
    const {
        enableCustomFormulaSwitch,
        customFormulaBuilderDiv,
        cellFormulaOperandSelect,
        currentCellFormulaDisplay,
        addCellOperandToFormulaBtn,
        clearCellFormulaBtn,
        cellSheetNameSelect,
        cellMetricColumnSelect,
        cellValueTypeSelect, // Added for potential disabling
        cellNumberOptionsContainer, // Added for potential disabling
        cellAggregationTypeSelect, // Added for potential disabling
        editingCellIdentifierInput,
    } = elements;

    currentCellFormulaDisplayElement = currentCellFormulaDisplay;
    cellFormulaOperandSelectElement = cellFormulaOperandSelect;
    cellConfigurationsRef = allCellConfigs; // Store reference to all cell configurations

    if (enableCustomFormulaSwitch) {
        enableCustomFormulaSwitch.addEventListener("change", function () {
            const isChecked = this.checked;
            if (customFormulaBuilderDiv)
                customFormulaBuilderDiv.style.display = isChecked
                    ? "block"
                    : "none";

            if (!isChecked) {
                currentFormula = [];
                renderCellFormulaDisplay();
            } else {
                populateCellFormulaOperands(cellSheetNameSelect.value);
            }
        });
    }

    if (customFormulaBuilderDiv) {
        customFormulaBuilderDiv
            .querySelectorAll(".operator-btn")
            .forEach((btn) => {
                btn.addEventListener("click", function () {
                    currentFormula.push({
                        type: "operator",
                        value: this.dataset.op,
                    });
                    renderCellFormulaDisplay();
                });
            });
    }

    if (addCellOperandToFormulaBtn) {
        addCellOperandToFormulaBtn.addEventListener("click", function () {
            const selectedValue = cellFormulaOperandSelectElement.value;
            if (selectedValue) {
                console.log(selectedValue);
                if (selectedValue === "number:custom") {
                    const numStr = prompt("Enter number:");
                    if (numStr !== null) {
                        // Check if prompt was n
                        // Check if prompt was not cancelled
                        const num = parseFloat(numStr);
                        if (!isNaN(num)) {
                            currentFormula.push({
                                type: "number",
                                value: num,
                            });
                        } else {
                            alert("Invalid number entered.");
                        }
                    }
                } else {
                    currentFormula.push({
                        type: "metric",
                        value: selectedValue,
                    });
                }
                renderCellFormulaDisplay();
                cellFormulaOperandSelectElement.value = ""; // Reset select
            }
        });
    }

    if (clearCellFormulaBtn) {
        clearCellFormulaBtn.addEventListener("click", function () {
            currentFormula = [];
            renderCellFormulaDisplay();
        });
    }

    return {
        populateOperands: populateCellFormulaOperands,
        renderFormula: renderCellFormulaDisplay,
        getFormula: () => currentFormula,
        setFormula: (formulaArray) => {
            currentFormula = Array.isArray(formulaArray)
                ? [...formulaArray]
                : [];
        },
        clearFormula: () => {
            currentFormula = [];
            renderCellFormulaDisplay();
        },
    };
}
