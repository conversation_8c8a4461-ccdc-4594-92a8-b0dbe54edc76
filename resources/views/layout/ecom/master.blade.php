<!doctype html>
<html lang="en" data-layout="vertical" data-topbar="light" data-sidebar="dark" data-sidebar-size="lg"
    data-sidebar-image="none" data-preloader="disable">

<head>

    <meta charset="utf-8" />
    <title>Networld Asia - Innovative</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="Premium Multipurpose Admin & Dashboard Template" name="description" />
    <meta content="Themesbrand" name="author" />
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!-- App favicon -->
    <link rel="shortcut icon" href="https://networldasia.com/wp-content/uploads/2022/04/Group.svg" type="image/x-icon">

    <!-- jsvectormap css -->
    <link href="{{url('/template/main')}}/assets/libs/jsvectormap/css/jsvectormap.min.css" rel="stylesheet"
        type="text/css" />

    <!--Swiper slider css-->
    <link href="{{url('/template/main')}}/assets/libs/swiper/swiper-bundle.min.css" rel="stylesheet" type="text/css" />

    <!-- Layout config Js -->
    <script src="{{url('/template/main')}}/assets/js/layout.js"></script>
    <!-- Bootstrap Css -->
    <link href="{{url('/template/main')}}/assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <!-- Icons Css -->
    <link href="{{url('/template/main')}}/assets/css/icons.min.css" rel="stylesheet" type="text/css" />
    <!-- App Css-->
    <link href="{{url('/template/main')}}/assets/css/app.min.css" rel="stylesheet" type="text/css" />
    <!-- custom Css-->
    <link href="{{url('/template/main')}}/assets/css/custom.min.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="{{ asset('libs/cdn/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css') }}">
    <link rel="stylesheet" href="{{url('/')}}/template/main/assets/css/custom.min.css">
    <link href="{{url('/template/main')}}/assets/libs/sweetalert2/sweetalert2.min.css" rel="stylesheet"
        type="text/css" />

    @yield('head')

    <style>
        .active-nwa {
            background: #546ab3;
            position: relative;

            z-index: 1;
        }

        .active-nwa::after {
            content: '';
            background: #546ab3;
            position: absolute;
            left: -100px;
            right: 0px;
            bottom: 0;
            top: 0;
            z-index: -1;
        }

        .choices__list--multiple .mapped::before {
            content: '\2713';
            color: white;
            border-radius: 50%;
            background: #197030;
            width: 15px;
            height: 15px;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            margin-right: 5px;
        }

        .page-content {
            padding: 0;
        }
    </style>

</head>

<body>

    <!-- Begin page -->
    <div id="layout-wrapper">
        {{-- @include('partials.header') --}}
        {{-- @include('partials.menus') --}}

        {{-- menu --}}
        <div class="app-menu navbar-menu">
            <!-- LOGO -->
            <div class="navbar-brand-box">
                <!-- Dark Logo-->
                <a href="{{ $view ? route('ecom-reports.view', ['report_type' => 'overall', 'report' => $report->id]) : route('ecom-reports.index') }}" class="logo logo-dark">
                    <span class="logo-sm">
                        <img src="{{url('/')}}/imgs/logo-sm.png" height="22">
                    </span>
                    <span class="logo-lg">
                        <img src="{{url('/')}}/imgs/NetworldAsia-logo.svg" alt="" height="50">
                    </span>
                </a>
                <!-- Light Logo-->
                <a href="{{ $view ? route('ecom-reports.view', ['report_type' => 'overall', 'report' => $report->id]) : route('ecom-reports.index') }}" class="logo logo-light">
                    <span class="logo-sm">
                        <img src="{{url('/')}}/imgs/logo-sm.png" height="22">
                    </span>
                    <span class="logo-lg">
                        <img src="{{url('/')}}/imgs/logo.png" alt="" height="50">
                    </span>
                </a>
                <button type="button" class="btn btn-sm p-0 fs-20 header-item float-end btn-vertical-sm-hover"
                    id="vertical-hover">
                    <i class="ri-record-circle-line"></i>
                </button>
            </div>

            <div id="scrollbar" data-simplebar="init" class="h-100 simplebar-scrollable-y">
                <div class="simplebar-wrapper" style="margin: 0px;">
                    <div class="simplebar-height-auto-observer-wrapper">
                        <div class="simplebar-height-auto-observer"></div>
                    </div>
                    <div class="simplebar-mask">
                        <div class="simplebar-offset" style="right: 0px; bottom: 0px;">
                            <div class="simplebar-content-wrapper" tabindex="0" role="region"
                                aria-label="scrollable content" style="height: 100%; overflow: hidden scroll;">
                                <div class="simplebar-content" style="padding: 0px;">
                                    <div class="container-fluid">


                                        <div id="two-column-menu">
                                        </div>
                                        <ul class="navbar-nav" id="navbar-nav" data-simplebar="init">
                                            <div class="simplebar-wrapper" style="margin: 0px;">
                                                <div class="simplebar-height-auto-observer-wrapper">
                                                    <div class="simplebar-height-auto-observer"></div>
                                                </div>
                                                <div class="simplebar-mask">
                                                    <div class="simplebar-offset" style="right: 0px; bottom: 0px;">
                                                        <div class="simplebar-content-wrapper" tabindex="0"
                                                            role="region" aria-label="scrollable content"
                                                            style="height: auto; overflow: hidden;">
                                                            <div class="simplebar-content" style="padding: 0px;">
                                                                @php
                                                                $currentReportType = request()->query('report_type');
                                                                $view = $view ?? false;

                                                                // Determine active state for main groups
                                                                $isOverallGroupActive = $currentReportType &&
                                                                str_starts_with($currentReportType, 'overall');
                                                                $isLazadaGroupActive = $currentReportType &&
                                                                str_starts_with($currentReportType, 'lazada-');
                                                                $isShopeeGroupActive = $currentReportType &&
                                                                str_starts_with($currentReportType, 'shopee-');
                                                                $isTikiGroupActive = $currentReportType &&
                                                                str_starts_with($currentReportType, 'tiki-');

                                                                $isOverallShopeeSubGroupActive = $currentReportType &&
                                                                str_starts_with($currentReportType, 'overall-shopee');
                                                                @endphp
                                                                <li class="menu-title"><span
                                                                        data-key="t-reports-menu">ECOM Reports</span>
                                                                </li>

                                                                <li class="nav-item">
                                                                    <a class="nav-link menu-link {{ !$isOverallGroupActive ? 'collapsed' : '' }}"
                                                                        href="#sidebarOverallReports"
                                                                        data-bs-toggle="collapse" role="button"
                                                                        aria-expanded="{{ $isOverallGroupActive ? 'true' : 'false' }}"
                                                                        aria-controls="sidebarOverallReports">
                                                                        <i class="ri-pie-chart-line"></i> <span
                                                                            data-key="t-overall-reports">OVERALL</span>
                                                                    </a>
                                                                    <div class="collapse menu-dropdown {{ $isOverallGroupActive ? 'show' : '' }}"
                                                                        id="sidebarOverallReports">
                                                                        <ul class="nav nav-sm flex-column">
                                                                            <li
                                                                                class="nav-item {{ $currentReportType == 'overall' ? 'active' : '' }}">
                                                                                <a href="{{ $view ? route('ecom-reports.view', ['report_type' => 'overall', 'report' => $report->id]) : route('ecom-reports.config', ['report_type' => 'overall', 'report' => $report->id]) }}"
                                                                                    class="nav-link">OVERALL</a>
                                                                            </li>
                                                                            <li
                                                                                class="nav-item {{ $currentReportType == 'overall-lazada' ? 'active' : '' }}">
                                                                                <a href="{{ $view ? route('ecom-reports.view', ['report_type' => 'overall-lazada', 'report' => $report->id]) : route('ecom-reports.config', ['report_type' => 'overall-lazada', 'report' => $report->id]) }}"
                                                                                    class="nav-link">OVERALL -
                                                                                    LAZADA</a>
                                                                            </li>
                                                                            <li
                                                                                class="nav-item {{ $currentReportType == 'overall-shopee' ? 'active' : '' }}">
                                                                                <a href="{{ $view ? route('ecom-reports.view', ['report_type' => 'overall-shopee', 'report' => $report->id]) : route('ecom-reports.config', ['report_type' => 'overall-shopee', 'report' => $report->id]) }}"
                                                                                    class="nav-link">OVERALL -
                                                                                    SHOPEE</a>
                                                                            </li>
                                                                            <li
                                                                                class="nav-item {{ $currentReportType == 'overall-shopee-sem-pmax' ? 'active' : '' }}">
                                                                                <a href="{{ $view ? route('ecom-reports.view', ['report_type' => 'overall-shopee-sem-pmax', 'report' => $report->id]) : route('ecom-reports.config', ['report_type' => 'overall-shopee-sem-pmax', 'report' => $report->id]) }}"
                                                                                    class="nav-link">OVERALL - SHOPEE -
                                                                                    SEM & PMAX</a>
                                                                            </li>
                                                                            <li
                                                                                class="nav-item {{ $currentReportType == 'overall-tiki' ? 'active' : '' }}">
                                                                                <a href="{{ $view ? route('ecom-reports.view', ['report_type' => 'overall-tiki', 'report' => $report->id]) : route('ecom-reports.config', ['report_type' => 'overall-tiki', 'report' => $report->id]) }}"
                                                                                    class="nav-link">OVERALL - TIKI</a>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>

                                                                <li class=" nav-item">
                                                                    <a class="nav-link menu-link {{ !$isLazadaGroupActive ? 'collapsed' : '' }}"
                                                                        href="#sidebarLazadaReports"
                                                                        data-bs-toggle="collapse" role="button"
                                                                        aria-expanded="{{ $isLazadaGroupActive ? 'true' : 'false' }}"
                                                                        aria-controls="sidebarLazadaReports">
                                                                        <i class="ri-store-2-line"></i>
                                                                        <span data-key="t-lazada-reports">LAZADA</span>
                                                                    </a>
                                                                    <div class="collapse menu-dropdown {{ $isLazadaGroupActive ? 'show' : '' }}"
                                                                        id="sidebarLazadaReports">
                                                                        <ul class="nav nav-sm flex-column">
                                                                            <li
                                                                                class="nav-item {{ $currentReportType == 'lazada-offsite-fb-conversion' ? 'active' : '' }}">
                                                                                <a href="{{ $view ? route('ecom-reports.view', ['report_type' => 'lazada-offsite-fb-conversion', 'report' => $report->id]) : route('ecom-reports.config', ['report_type' => 'lazada-offsite-fb-conversion', 'report' => $report->id]) }}"
                                                                                    class="nav-link"
                                                                                    data-key="t-lazada-offsite-fb-conversion">LAZADA
                                                                                    - OFFSITE - FACEBOOK CONVERSION</a>
                                                                            </li>
                                                                            <li
                                                                                class="nav-item {{ $currentReportType == 'lazada-offsite-fb-cpas' ? 'active' : '' }}">
                                                                                <a href="{{ $view ? route('ecom-reports.view', ['report_type' => 'lazada-offsite-fb-cpas', 'report' => $report->id]) : route('ecom-reports.config', ['report_type' => 'lazada-offsite-fb-cpas', 'report' => $report->id]) }}"
                                                                                    class="nav-link"
                                                                                    data-key="t-lazada-offsite-fb-cpas">LAZADA
                                                                                    - OFFSITE - FACEBOOK CPAS</a>
                                                                            </li>
                                                                            <li
                                                                                class="nav-item {{ $currentReportType == 'lazada-onsite-search-discovery' ? 'active' : '' }}">
                                                                                <a href="{{ $view ? route('ecom-reports.view', ['report_type' => 'lazada-onsite-search-discovery', 'report' => $report->id]) : route('ecom-reports.config', ['report_type' => 'lazada-onsite-search-discovery', 'report' => $report->id]) }}"
                                                                                    class="nav-link"
                                                                                    data-key="t-lazada-onsite-search-discovery">LAZADA
                                                                                    - ONSITE - SEARCH / DISCOVERY</a>
                                                                            </li>
                                                                            <li
                                                                                class="nav-item {{ $currentReportType == 'lazada-onsite-affiliate' ? 'active' : '' }}">
                                                                                <a href="{{ $view ? route('ecom-reports.view', ['report_type' => 'lazada-onsite-affiliate', 'report' => $report->id]) : route('ecom-reports.config', ['report_type' => 'lazada-onsite-affiliate', 'report' => $report->id]) }}"
                                                                                    class="nav-link"
                                                                                    data-key="t-lazada-onsite-affiliate">LAZADA
                                                                                    - ONSITE - AFFILIATE</a>
                                                                            </li>
                                                                            <li
                                                                                class="nav-item {{ $currentReportType == 'lazada-organic-untrackable' ? 'active' : '' }}">
                                                                                <a href="{{ $view ? route('ecom-reports.view', ['report_type' => 'lazada-organic-untrackable', 'report' => $report->id]) : route('ecom-reports.config', ['report_type' => 'lazada-organic-untrackable', 'report' => $report->id]) }}"
                                                                                    class="nav-link"
                                                                                    data-key="t-lazada-organic-untrackable">LAZADA
                                                                                    - ORGANIC - UNTRACKABLE SOURCE</a>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>

                                                                <li class="nav-item">
                                                                    <a class="nav-link menu-link {{ !$isShopeeGroupActive ? 'collapsed' : '' }}"
                                                                        href="#sidebarShopeeReports"
                                                                        data-bs-toggle="collapse" role="button"
                                                                        aria-expanded="{{ $isShopeeGroupActive ? 'true' : 'false' }}"
                                                                        aria-controls="sidebarShopeeReports">
                                                                        <i class="ri-shopping-bag-3-line"></i>
                                                                        <span data-key="t-shopee-reports">SHOPEE</span>
                                                                    </a>
                                                                    <div class="collapse menu-dropdown {{ $isShopeeGroupActive ? 'show' : '' }}"
                                                                        id="sidebarShopeeReports">
                                                                        <ul class="nav nav-sm flex-column">
                                                                            <li
                                                                                class="nav-item {{ $currentReportType == 'shopee-offsite-google-sem' ? 'active' : '' }}">
                                                                                <a href="{{ $view ? route('ecom-reports.view', ['report_type' => 'shopee-offsite-google-sem', 'report' => $report->id]) : route('ecom-reports.config', ['report_type' => 'shopee-offsite-google-sem', 'report' => $report->id]) }}"
                                                                                    class="nav-link"
                                                                                    data-key="t-shopee-offsite-google-sem">SHOPEE
                                                                                    - OFFSITE - GOOGLE SEM</a>
                                                                            </li>
                                                                            <li
                                                                                class="nav-item {{ $currentReportType == 'shopee-offsite-fb-conversion' ? 'active' : '' }}">
                                                                                <a href="{{ $view ? route('ecom-reports.view', ['report_type' => 'shopee-offsite-fb-conversion', 'report' => $report->id]) : route('ecom-reports.config', ['report_type' => 'shopee-offsite-fb-conversion', 'report' => $report->id]) }}"
                                                                                    class="nav-link"
                                                                                    data-key="t-shopee-offsite-fb-conversion">SHOPEE
                                                                                    - OFFSITE - FACEBOOK CONVERSION</a>
                                                                            </li>
                                                                            <li
                                                                                class="nav-item {{ $currentReportType == 'shopee-offsite-fb-cpas' ? 'active' : '' }}">
                                                                                <a href="{{ $view ? route('ecom-reports.view', ['report_type' => 'shopee-offsite-fb-cpas', 'report' => $report->id]) : route('ecom-reports.config', ['report_type' => 'shopee-offsite-fb-cpas', 'report' => $report->id]) }}"
                                                                                    class="nav-link"
                                                                                    data-key="t-shopee-offsite-fb-cpas">SHOPEE
                                                                                    - OFFSITE - FACEBOOK CPAS</a>
                                                                            </li>
                                                                            <li
                                                                                class="nav-item {{ $currentReportType == 'shopee-offsite-youtube-trueview-action' ? 'active' : '' }}">
                                                                                <a href="{{ $view ? route('ecom-reports.view', ['report_type' => 'shopee-offsite-youtube-trueview-action', 'report' => $report->id]) : route('ecom-reports.config', ['report_type' => 'shopee-offsite-youtube-trueview-action', 'report' => $report->id]) }}"
                                                                                    class="nav-link"
                                                                                    data-key="t-shopee-offsite-youtube-trueview-action">SHOPEE
                                                                                    - OFFSITE - YOUTUBE TRUEVIEW
                                                                                    ACTION</a>
                                                                            </li>
                                                                            <li
                                                                                class="nav-item {{ $currentReportType == 'shopee-onsite-search-discovery' ? 'active' : '' }}">
                                                                                <a href="{{ $view ? route('ecom-reports.view', ['report_type' => 'shopee-onsite-search-discovery', 'report' => $report->id]) : route('ecom-reports.config', ['report_type' => 'shopee-onsite-search-discovery', 'report' => $report->id]) }}"
                                                                                    class="nav-link"
                                                                                    data-key="t-shopee-onsite-search-discovery">SHOPEE
                                                                                    - ONSITE - SEARCH / DISCOVERY</a>
                                                                            </li>
                                                                            <li
                                                                                class="nav-item {{ $currentReportType == 'shopee-onsite-affiliate' ? 'active' : '' }}">
                                                                                <a href="{{ $view ? route('ecom-reports.view', ['report_type' => 'shopee-onsite-affiliate', 'report' => $report->id]) : route('ecom-reports.config', ['report_type' => 'shopee-onsite-affiliate', 'report' => $report->id]) }}"
                                                                                    class="nav-link"
                                                                                    data-key="t-shopee-onsite-affiliate">SHOPEE
                                                                                    - ONSITE - AFFILIATE</a>
                                                                            </li>
                                                                            <li
                                                                                class="nav-item {{ $currentReportType == 'shopee-organic-untrackable' ? 'active' : '' }}">
                                                                                <a href="{{ $view ? route('ecom-reports.view', ['report_type' => 'shopee-organic-untrackable', 'report' => $report->id]) : route('ecom-reports.config', ['report_type' => 'shopee-organic-untrackable', 'report' => $report->id]) }}"
                                                                                    class="nav-link"
                                                                                    data-key="t-shopee-organic-untrackable">SHOPEE
                                                                                    - ORGANIC - UNTRACKABLE SOURCE</a>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>

                                                                <li class="nav-item">
                                                                    <a class="nav-link menu-link {{ !$isTikiGroupActive ? 'collapsed' : '' }}"
                                                                        href="#sidebarTikiReports"
                                                                        data-bs-toggle="collapse" role="button"
                                                                        aria-expanded="{{ $isTikiGroupActive ? 'true' : 'false' }}"
                                                                        aria-controls="sidebarTikiReports">
                                                                        <i class="ri-price-tag-3-line"></i>
                                                                        <span data-key="t-tiki-reports">TIKI</span>
                                                                    </a>
                                                                    <div class="collapse menu-dropdown {{ $isTikiGroupActive ? 'show' : '' }}"
                                                                        id="sidebarTikiReports">
                                                                        <ul class="nav nav-sm flex-column">
                                                                            <li
                                                                                class="nav-item {{ $currentReportType == 'tiki-onsite-search-discovery' ? 'active' : '' }}">
                                                                                <a href="{{ $view ? route('ecom-reports.view', ['report_type' => 'tiki-onsite-search-discovery', 'report' => $report->id]) : route('ecom-reports.config', ['report_type' => 'tiki-onsite-search-discovery', 'report' => $report->id]) }}"
                                                                                    class="nav-link"
                                                                                    data-key="t-tiki-onsite-search-discovery">TIKI
                                                                                    - ONSITE - SEARCH / DISCOVERY</a>
                                                                            </li>
                                                                            <li
                                                                                class="nav-item {{ $currentReportType == 'tiki-organic-untrackable' ? 'active' : '' }}">
                                                                                <a href="{{ $view ? route('ecom-reports.view', ['report_type' => 'tiki-organic-untrackable', 'report' => $report->id]) : route('ecom-reports.config', ['report_type' => 'tiki-organic-untrackable', 'report' => $report->id]) }}"
                                                                                    class="nav-link"
                                                                                    data-key="t-tiki-organic-untrackable">TIKI
                                                                                    - ORGANIC - UNTRACKABLE SOURCE</a>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </li>
                                                                </li>

                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="simplebar-placeholder"
                                                    style="width: 249px; height: 1754px;"></div>
                                            </div>
                                            <div class="simplebar-track simplebar-horizontal"
                                                style="visibility: hidden;">
                                                <div class="simplebar-scrollbar"
                                                    style="width: 0px; display: none; transform: translate3d(0px, 0px, 0px);">
                                                </div>
                                            </div>
                                            <div class="simplebar-track simplebar-vertical" style="visibility: hidden;">
                                                <div class="simplebar-scrollbar" style="height: 0px; display: none;">
                                                </div>
                                            </div>
                                            <div class="simplebar-track simplebar-horizontal">
                                                <div class="simplebar-scrollbar"></div>
                                            </div>
                                            <div class="simplebar-track simplebar-vertical">
                                                <div class="simplebar-scrollbar"></div>
                                            </div>
                                        </ul>
                                    </div>
                                    <!-- Sidebar -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="simplebar-placeholder" style="width: 249px; height: 1754px;"></div>
                </div>
                <div class="simplebar-track simplebar-horizontal" style="visibility: hidden;">
                    <div class="simplebar-scrollbar"
                        style="width: 0px; display: none; transform: translate3d(0px, 0px, 0px);"></div>
                </div>
                <div class="simplebar-track simplebar-vertical" style="visibility: visible;">
                    <div class="simplebar-scrollbar"
                        style="height: 123px; display: block; transform: translate3d(0px, 0px, 0px);"></div>
                </div>
            </div>

            <div class="sidebar-background"></div>
        </div>

        <!-- Vertical Overlay-->
        <div class="vertical-overlay"></div>

        <!-- ============================================================== -->
        <!-- Start right Content here -->
        <!-- ============================================================== -->
        <div class="main-content">

            <div class="page-content">
                <div class="container-fluid">

                    @yield('main-content')

                </div>
                <!-- container-fluid -->
            </div>
            <!-- End Page-content -->

            <footer class="footer">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-sm-6">
                            <script>
                                document.write(new Date().getFullYear())
                            </script> © <a class="" href="https://networldasia.com" target="_blank">Networld Asia.</a>
                        </div>
                        <div class="col-sm-6">
                            <div class="text-sm-end d-none d-sm-block">
                                Design & Develop by Networld Asia
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
        <!-- end main content-->

    </div>
    <!-- END layout-wrapper -->



    <!--start back-to-top-->
    <button onclick="topFunction()" class="btn btn-danger btn-icon" id="back-to-top">
        <i class="ri-arrow-up-line"></i>
    </button>
    <!--end back-to-top-->

    <!--preloader-->
    <div id="preloader">
        <div id="status">
            <div class="spinner-border text-primary avatar-sm" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    </div>


    <div class="customizer-setting d-none d-md-block">
        <div class="btn-info btn-rounded shadow-lg btn btn-icon btn-lg p-2" data-bs-toggle="offcanvas"
            data-bs-target="#theme-settings-offcanvas" aria-controls="theme-settings-offcanvas">
            <i class='mdi mdi-spin mdi-cog-outline fs-22'></i>
        </div>
    </div>

    <!-- Theme Settings -->
    <div class="offcanvas offcanvas-end border-0" tabindex="-1" id="theme-settings-offcanvas">
        <div class="d-flex align-items-center bg-primary bg-gradient p-3 offcanvas-header">
            <h5 class="m-0 me-2 text-white">Theme Customizer</h5>

            <button type="button" class="btn-close btn-close-white ms-auto" data-bs-dismiss="offcanvas"
                aria-label="Close"></button>
        </div>
        <div class="offcanvas-body p-0">
            <div data-simplebar class="h-100">
                <div class="p-4">
                    <h6 class="mb-0 fw-semibold text-uppercase">Layout</h6>
                    <p class="text-muted">Choose your layout</p>

                    <div class="row">
                        <div class="col-4">
                            <div class="form-check card-radio">
                                <input id="customizer-layout01" name="data-layout" type="radio" value="vertical"
                                    class="form-check-input">
                                <label class="form-check-label p-0 avatar-md w-100" for="customizer-layout01">
                                    <span class="d-flex gap-1 h-100">
                                        <span class="flex-shrink-0">
                                            <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                <span class="d-block p-1 px-2 bg-soft-primary rounded mb-2"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                            </span>
                                        </span>
                                        <span class="flex-grow-1">
                                            <span class="d-flex h-100 flex-column">
                                                <span class="bg-light d-block p-1"></span>
                                                <span class="bg-light d-block p-1 mt-auto"></span>
                                            </span>
                                        </span>
                                    </span>
                                </label>
                            </div>
                            <h5 class="fs-13 text-center mt-2">Vertical</h5>
                        </div>
                        <div class="col-4">
                            <div class="form-check card-radio">
                                <input id="customizer-layout02" name="data-layout" type="radio" value="horizontal"
                                    class="form-check-input">
                                <label class="form-check-label p-0 avatar-md w-100" for="customizer-layout02">
                                    <span class="d-flex h-100 flex-column gap-1">
                                        <span class="bg-light d-flex p-1 gap-1 align-items-center">
                                            <span class="d-block p-1 bg-soft-primary rounded me-1"></span>
                                            <span class="d-block p-1 pb-0 px-2 bg-soft-primary ms-auto"></span>
                                            <span class="d-block p-1 pb-0 px-2 bg-soft-primary"></span>
                                        </span>
                                        <span class="bg-light d-block p-1"></span>
                                        <span class="bg-light d-block p-1 mt-auto"></span>
                                    </span>
                                </label>
                            </div>
                            <h5 class="fs-13 text-center mt-2">Horizontal</h5>
                        </div>
                        <div class="col-4">
                            <div class="form-check card-radio">
                                <input id="customizer-layout03" name="data-layout" type="radio" value="twocolumn"
                                    class="form-check-input">
                                <label class="form-check-label p-0 avatar-md w-100" for="customizer-layout03">
                                    <span class="d-flex gap-1 h-100">
                                        <span class="flex-shrink-0">
                                            <span class="bg-light d-flex h-100 flex-column gap-1">
                                                <span class="d-block p-1 bg-soft-primary mb-2"></span>
                                                <span class="d-block p-1 pb-0 bg-soft-primary"></span>
                                                <span class="d-block p-1 pb-0 bg-soft-primary"></span>
                                                <span class="d-block p-1 pb-0 bg-soft-primary"></span>
                                            </span>
                                        </span>
                                        <span class="flex-shrink-0">
                                            <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                            </span>
                                        </span>
                                        <span class="flex-grow-1">
                                            <span class="d-flex h-100 flex-column">
                                                <span class="bg-light d-block p-1"></span>
                                                <span class="bg-light d-block p-1 mt-auto"></span>
                                            </span>
                                        </span>
                                    </span>
                                </label>
                            </div>
                            <h5 class="fs-13 text-center mt-2">Two Column</h5>
                        </div>
                        <!-- end col -->
                    </div>

                    <h6 class="mt-4 mb-0 fw-semibold text-uppercase">Color Scheme</h6>
                    <p class="text-muted">Choose Light or Dark Scheme.</p>

                    <div class="colorscheme-cardradio">
                        <div class="row">
                            <div class="col-4">
                                <div class="form-check card-radio">
                                    <input class="form-check-input" type="radio" name="data-layout-mode"
                                        id="layout-mode-light" value="light">
                                    <label class="form-check-label p-0 avatar-md w-100" for="layout-mode-light">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0">
                                                <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                    <span class="d-block p-1 px-2 bg-soft-primary rounded mb-2"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Light</h5>
                            </div>

                            <div class="col-4">
                                <div class="form-check card-radio dark">
                                    <input class="form-check-input" type="radio" name="data-layout-mode"
                                        id="layout-mode-dark" value="dark">
                                    <label class="form-check-label p-0 avatar-md w-100 bg-dark" for="layout-mode-dark">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0">
                                                <span class="bg-soft-light d-flex h-100 flex-column gap-1 p-1">
                                                    <span class="d-block p-1 px-2 bg-soft-light rounded mb-2"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-light"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-light"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-light"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-soft-light d-block p-1"></span>
                                                    <span class="bg-soft-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Dark</h5>
                            </div>
                        </div>
                    </div>

                    <div id="layout-width">
                        <h6 class="mt-4 mb-0 fw-semibold text-uppercase">Layout Width</h6>
                        <p class="text-muted">Choose Fluid or Boxed layout.</p>

                        <div class="row">
                            <div class="col-4">
                                <div class="form-check card-radio">
                                    <input class="form-check-input" type="radio" name="data-layout-width"
                                        id="layout-width-fluid" value="fluid">
                                    <label class="form-check-label p-0 avatar-md w-100" for="layout-width-fluid">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0">
                                                <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                    <span class="d-block p-1 px-2 bg-soft-primary rounded mb-2"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Fluid</h5>
                            </div>
                            <div class="col-4">
                                <div class="form-check card-radio">
                                    <input class="form-check-input" type="radio" name="data-layout-width"
                                        id="layout-width-boxed" value="boxed">
                                    <label class="form-check-label p-0 avatar-md w-100 px-2" for="layout-width-boxed">
                                        <span class="d-flex gap-1 h-100 border-start border-end">
                                            <span class="flex-shrink-0">
                                                <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                    <span class="d-block p-1 px-2 bg-soft-primary rounded mb-2"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Boxed</h5>
                            </div>
                        </div>
                    </div>

                    <div id="layout-position">
                        <h6 class="mt-4 mb-0 fw-semibold text-uppercase">Layout Position</h6>
                        <p class="text-muted">Choose Fixed or Scrollable Layout Position.</p>

                        <div class="btn-group radio" role="group">
                            <input type="radio" class="btn-check" name="data-layout-position" id="layout-position-fixed"
                                value="fixed">
                            <label class="btn btn-light w-sm" for="layout-position-fixed">Fixed</label>

                            <input type="radio" class="btn-check" name="data-layout-position"
                                id="layout-position-scrollable" value="scrollable">
                            <label class="btn btn-light w-sm ms-0" for="layout-position-scrollable">Scrollable</label>
                        </div>
                    </div>
                    <h6 class="mt-4 mb-0 fw-semibold text-uppercase">Topbar Color</h6>
                    <p class="text-muted">Choose Light or Dark Topbar Color.</p>
                    \creatives
                    <div class="row">
                        <div class="col-4">
                            <div class="form-check card-radio">
                                <input class="form-check-input" type="radio" name="data-topbar" id="topbar-color-light"
                                    value="light">
                                <label class="form-check-label p-0 avatar-md w-100" for="topbar-color-light">
                                    <span class="d-flex gap-1 h-100">
                                        <span class="flex-shrink-0">
                                            <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                <span class="d-block p-1 px-2 bg-soft-primary rounded mb-2"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                            </span>
                                        </span>
                                        <span class="flex-grow-1">
                                            <span class="d-flex h-100 flex-column">
                                                <span class="bg-light d-block p-1"></span>
                                                <span class="bg-light d-block p-1 mt-auto"></span>
                                            </span>
                                        </span>
                                    </span>
                                </label>
                            </div>
                            <h5 class="fs-13 text-center mt-2">Light</h5>
                        </div>
                        <div class="col-4">
                            <div class="form-check card-radio">
                                <input class="form-check-input" type="radio" name="data-topbar" id="topbar-color-dark"
                                    value="dark">
                                <label class="form-check-label p-0 avatar-md w-100" for="topbar-color-dark">
                                    <span class="d-flex gap-1 h-100">
                                        <span class="flex-shrink-0">
                                            <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                <span class="d-block p-1 px-2 bg-soft-primary rounded mb-2"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                            </span>
                                        </span>
                                        <span class="flex-grow-1">
                                            <span class="d-flex h-100 flex-column">
                                                <span class="bg-primary d-block p-1"></span>
                                                <span class="bg-light d-block p-1 mt-auto"></span>
                                            </span>
                                        </span>
                                    </span>
                                </label>
                            </div>
                            <h5 class="fs-13 text-center mt-2">Dark</h5>
                        </div>
                    </div>

                    <div id="sidebar-size">
                        <h6 class="mt-4 mb-0 fw-semibold text-uppercase">Sidebar Size</h6>
                        <p class="text-muted">Choose a size of Sidebar.</p>

                        <div class="row">
                            <div class="col-4">
                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-sidebar-size"
                                        id="sidebar-size-default" value="lg">
                                    <label class="form-check-label p-0 avatar-md w-100" for="sidebar-size-default">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0">
                                                <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                    <span class="d-block p-1 px-2 bg-soft-primary rounded mb-2"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Default</h5>
                            </div>

                            <div class="col-4">
                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-sidebar-size"
                                        id="sidebar-size-compact" value="md">
                                    <label class="form-check-label p-0 avatar-md w-100" for="sidebar-size-compact">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0">
                                                <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                    <span class="d-block p-1 bg-soft-primary rounded mb-2"></span>
                                                    <span class="d-block p-1 pb-0 bg-soft-primary"></span>
                                                    <span class="d-block p-1 pb-0 bg-soft-primary"></span>
                                                    <span class="d-block p-1 pb-0 bg-soft-primary"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Compact</h5>
                            </div>

                            <div class="col-4">
                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-sidebar-size"
                                        id="sidebar-size-small" value="sm">
                                    <label class="form-check-label p-0 avatar-md w-100" for="sidebar-size-small">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0">
                                                <span class="bg-light d-flex h-100 flex-column gap-1">
                                                    <span class="d-block p-1 bg-soft-primary mb-2"></span>
                                                    <span class="d-block p-1 pb-0 bg-soft-primary"></span>
                                                    <span class="d-block p-1 pb-0 bg-soft-primary"></span>
                                                    <span class="d-block p-1 pb-0 bg-soft-primary"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Small (Icon View)</h5>
                            </div>

                            <div class="col-4">
                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-sidebar-size"
                                        id="sidebar-size-small-hover" value="sm-hover">
                                    <label class="form-check-label p-0 avatar-md w-100" for="sidebar-size-small-hover">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0">
                                                <span class="bg-light d-flex h-100 flex-column gap-1">
                                                    <span class="d-block p-1 bg-soft-primary mb-2"></span>
                                                    <span class="d-block p-1 pb-0 bg-soft-primary"></span>
                                                    <span class="d-block p-1 pb-0 bg-soft-primary"></span>
                                                    <span class="d-block p-1 pb-0 bg-soft-primary"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Small Hover View</h5>
                            </div>
                        </div>
                    </div>

                    <div id="sidebar-view">
                        <h6 class="mt-4 mb-0 fw-semibold text-uppercase">Sidebar View</h6>
                        <p class="text-muted">Choose Default or Detached Sidebar view.</p>

                        <div class="row">
                            <div class="col-4">
                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-layout-style"
                                        id="sidebar-view-default" value="default">
                                    <label class="form-check-label p-0 avatar-md w-100" for="sidebar-view-default">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0">
                                                <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                    <span class="d-block p-1 px-2 bg-soft-primary rounded mb-2"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Default</h5>
                            </div>
                            <div class="col-4">
                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-layout-style"
                                        id="sidebar-view-detached" value="detached">
                                    <label class="form-check-label p-0 avatar-md w-100" for="sidebar-view-detached">
                                        <span class="d-flex h-100 flex-column">
                                            <span class="bg-light d-flex p-1 gap-1 align-items-center px-2">
                                                <span class="d-block p-1 bg-soft-primary rounded me-1"></span>
                                                <span class="d-block p-1 pb-0 px-2 bg-soft-primary ms-auto"></span>
                                                <span class="d-block p-1 pb-0 px-2 bg-soft-primary"></span>
                                            </span>
                                            <span class="d-flex gap-1 h-100 p-1 px-2">
                                                <span class="flex-shrink-0">
                                                    <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                        <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                        <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                        <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                    </span>
                                                </span>
                                            </span>
                                            <span class="bg-light d-block p-1 mt-auto px-2"></span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Detached</h5>
                            </div>
                        </div>
                    </div>
                    <div id="sidebar-color">
                        <h6 class="mt-4 mb-0 fw-semibold text-uppercase">Sidebar Color</h6>
                        <p class="text-muted">Choose Ligth or Dark Sidebar Color.</p>

                        <div class="row">
                            <div class="col-4">
                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-sidebar"
                                        id="sidebar-color-light" value="light">
                                    <label class="form-check-label p-0 avatar-md w-100" for="sidebar-color-light">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0">
                                                <span class="bg-white border-end d-flex h-100 flex-column gap-1 p-1">
                                                    <span class="d-block p-1 px-2 bg-soft-primary rounded mb-2"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-primary"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Light</h5>
                            </div>
                            <div class="col-4">
                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-sidebar"
                                        id="sidebar-color-dark" value="dark">
                                    <label class="form-check-label p-0 avatar-md w-100" for="sidebar-color-dark">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0">
                                                <span class="bg-primary d-flex h-100 flex-column gap-1 p-1">
                                                    <span class="d-block p-1 px-2 bg-soft-light rounded mb-2"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-light"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-light"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-soft-light"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Dark</h5>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

        </div>
        <div class="offcanvas-footer border-top p-3 text-center">
            <div class="row">
                <div class="col-6">
                    <button type="button" class="btn btn-light w-100" id="reset-layout">Reset</button>
                </div>
                <div class="col-6">
                    <a href="#" target="_blank" class="btn btn-primary w-100">Buy Now</a>
                </div>
            </div>
        </div>
    </div>

    <!-- JAVASCRIPT -->
    <script src="{{url('/template/main')}}/assets/libs/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="{{url('/template/main')}}/assets/libs/simplebar/simplebar.min.js"></script>
    <script src="{{url('/template/main')}}/assets/libs/node-waves/waves.min.js"></script>
    <script src="{{url('/template/main')}}/assets/libs/feather-icons/feather.min.js"></script>
    <script src="{{url('/template/main')}}/assets/js/pages/plugins/lord-icon-2.1.0.js"></script>


    <script src="{{ asset('libs/cdn/toastify/toastify-js.js') }}"></script>
    <script src="{{url('/template/main')}}/assets/libs/flatpickr/flatpickr.min.js"></script>
    <script src="{{url('/template/main')}}/assets/libs/choices/choices.min.js"></script>

    <!-- apexcharts -->
    <script src="{{url('/template/main')}}/assets/libs/apexcharts/apexcharts.min.js"></script>

    <!-- Vector map-->
    <script src="{{url('/template/main')}}/assets/libs/jsvectormap/js/jsvectormap.min.js"></script>
    <script src="{{url('/template/main')}}/assets/libs/jsvectormap/maps/world-merc.js"></script>

    <!--Swiper slider js-->
    <script src="{{url('/template/main')}}/assets/libs/swiper/swiper-bundle.min.js"></script>

    <!-- Dashboard init -->
    <!-- <script src="{{url('/template/main')}}/assets/js/pages/dashboard-ecommerce.init.js"></script> -->

    <!-- App js -->
    <script src="{{url('/template/main')}}/assets/js/app.js"></script>

    <script src="{{url('/template/main')}}/assets/libs/sweetalert2/sweetalert2.min.js"></script>

    <script src="{{ mix('js/app.js') }}"></script>

    @yield('script')



</body>

</html>