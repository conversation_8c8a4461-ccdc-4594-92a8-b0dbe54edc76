<!-- prismjs plugin -->
<script src="{{url('/template/main')}}/assets/libs/prismjs/prism.js"></script>

<!-- list.js min js -->
<script src="{{url('/template/main')}}/assets/libs/list.js/list.min.js"></script>
<script src="{{url('/template/main')}}/assets/libs/list.pagination.js/list.pagination.min.js"></script>
<script src="{{url('/template/main')}}/assets/libs/jquery/jquery.min.js"></script>
<script src="{{url('/template/main')}}/assets/libs/moment/min/moment.min.js"></script>
<script src="{{url('/template/main')}}/assets/libs/daterangepicker/daterangepicker.min.js"></script>
<script src="{{url('/template/main')}}/assets/libs/select2/select2.min.js"></script>

<!-- apexcharts -->
<script src="{{url('/template/main')}}/assets/libs/apexcharts/apexcharts.min.js"></script>

<!-- select chosen -->
<script src="{{url('/template/main')}}/assets/libs/chosen/chosen.jquery.min.js"></script>

<!-- DataTables JavaScript -->
<script src="{{url('/template/main')}}/assets/libs/datatable/jquery.dataTables.min.js"></script>
<script src="{{url('/template/main')}}/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
<script src="{{url('/template/main')}}/assets/libs/datatable/dataTables.colReorder.min.js"></script>

{{-- for api --}}
<script>
    const sheetId = "{{ $report->sheet_id ?? '' }}";
    const reportId = "{{ $report->id ?? '' }}";
    const tableId = "{{ request()->query('report_type') ?? '' }}";
    const onlyView = "{{ $view ?? false }}";
    const apiGetWeeklyCommentsUrl = "{{ route('ecom-reports.apis.weekly-comments', ['report' => $report->id]) }}";
    const apiGetCellConfigurationsUrl = `{{ route('ecom-reports.apis.get-cell-configurations', ['report' => $report->id]) }}?table_id=${tableId}`;
    const apiGetSheetNamesUrl = `{{ route('ecom-reports.apis.get-sheet-names', ['report' => $report->id]) }}`;
    const apiPostGetSheetColumnsUrl = `{{ route('ecom-reports.apis.get-sheet-columns', ['report' => $report->id]) }}`;
    const apiSaveCellConfigurationUrl = `{{ route('ecom-reports.apis.save-cell-configuration', ['report' => $report->id]) }}`;
    const apiGetReportDataUrl = "{{ route('ecom-reports.apis.get-report-data', ['report' => $report->id]) }}";

    // Make reportId available globally for the JavaScript modules
    window.reportId = reportId;
    window.sheetId = sheetId;
    window.tableId = tableId;
    window.onlyView = onlyView;
    window.apiGetWeeklyCommentsUrl = apiGetWeeklyCommentsUrl;
    window.apiGetCellConfigurationsUrl = apiGetCellConfigurationsUrl;
    window.apiGetSheetNamesUrl = apiGetSheetNamesUrl;
    window.apiPostGetSheetColumnsUrl = apiPostGetSheetColumnsUrl;
    window.apiSaveCellConfigurationUrl = apiSaveCellConfigurationUrl;
    window.apiGetReportDataUrl = apiGetReportDataUrl;
</script>

<script src="{{ url('js/pages/ecom-report.js') }}"></script>