@extends('layout.master')
<meta name="csrf-token" content="{{ csrf_token() }}" />
<script src="{{ asset('libs/cdn/ajax/libs/jquery/2.1.1/jquery.min.js') }}"></script>
@section('head')
<!-- Styles -->
<link rel="stylesheet" type="text/css" href="{{url('/template/main')}}/assets/css/select2.min.css" />
<link rel="stylesheet" type="text/css" href="{{url('/template/main')}}/assets/css/select2-bootstrap-5-theme.min.css" />
<link rel="stylesheet" type="text/css" href="{{ asset('css/creative/index.css') }}" />
<style>
    .report-type.tiktok {
        background-color: #000;
        color: #fff;
    }
</style>
@endsection
@section('main-content')

<!-- start page title -->
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">ECOM Reports</h4>

            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="{{ route('index') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="javascript: void(0);">Reports</a></li>
                    <li class="breadcrumb-item active">ECOM Reports</li>
                </ol>
            </div>

        </div>
    </div>
</div>
<!-- end page title -->

<div class="row ecom-report-list">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Report List</h5>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                        data-bs-target="#createEcomReportModal">
                        <i class="ri-add-line align-bottom me-1"></i> Create ECOM Report
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover align-middle table-nowrap mb-0 creative-inactive">
                        <thead>
                            <tr style="font-size: 14px;">
                                <th scope="col">ID</th>
                                <th scope="col">Name</th>
                                <th scope="col">Type</th>
                                <th scope="col">Status</th>
                                <th scope="col">Creator</th>
                                <th scope="col">Updater</th>
                                <th scope="col" class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($reports ?? [] as $report)
                            <tr>
                                <td>#{{ $report->id }}</td>
                                <td>{{ $report->name }}</td>
                                <td>
                                    @if($report->type)
                                    <span
                                        class="badge badge-soft-secondary report-type {{ strtolower($report->type ?? "") }}">{{
                                        ucfirst(str_replace('_', ' ', $report->type))
                                        }}</span>
                                    @else
                                    <span class="badge badge-soft-secondary">Not Set</span>
                                    @endif
                                </td>
                                <td>@if($report->status)
                                    <div class="badge badge-soft-success">Active</div>
                                    @else
                                    <div class="badge badge-soft-warning">Inactive</div>
                                    @endif
                                </td>
                                <td>{{ $report->creator->name ?? '' }}</td>
                                <td>{{ $report->updater->name ?? '' }}</td>
                                <td style="text-align: left;">
                                    <button type="button"
                                        class="btn btn-sm btn-outline-primary btn-icon waves-effect waves-light edit-report-btn"
                                        data-bs-toggle="modal" data-bs-target="#editEcomReportModal"
                                        data-report-id="{{ $report->id }}" data-report-name="{{ $report->name }}"
                                        data-report-type="{{ $report->type ?? 'lazada' }}"
                                        data-report-sheet-id="{{ $report->sheet_id }}">
                                        <i class="ri-edit-2-line"></i>
                                    </button>
                                    <a
                                        href="{{ route('ecom-reports.config', ['report' => $report->id, 'report_type' => 'overall']) }}">
                                        <button type="button"
                                            class="btn btn-sm btn-outline-primary btn-icon waves-effect waves-light">
                                            <i class="ri-settings-2-fill"></i>
                                        </button>
                                    </a>
                                    <a
                                        href="{{ route('ecom-reports.view', ['report' => $report->id, 'report_type' => 'overall']) }}">
                                        <button type="button"
                                            class="btn btn-sm btn-outline-primary btn-icon waves-effect waves-light">
                                            <i class="ri-eye-2-line"></i>
                                        </button>
                                    </a>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

            </div>
        </div>

    </div>
</div>

{{-- End new table --}}

<!-- Create ECOM Report Modal -->
@include('reports.ecom.modals.create')
<!-- End Create ECOM Report Modal -->

<!-- Edit ECOM Report Modal -->
@include('reports.ecom.modals.edit')
<!-- End Edit ECOM Report Modal -->

@endsection
@section('script')
<!-- prismjs plugin -->
<script src="{{url('/template/main')}}/assets/libs/prismjs/prism.js"></script>
<!-- Scripts select2 -->
<script src="{{url('/template/main')}}/assets/libs/select2/select2.min.js"></script>

<!-- DataTables JavaScript -->
<script src="{{url('/template/main')}}/assets/libs/datatable/jquery.dataTables.min.js"></script>
<script src="{{url('/template/main')}}/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
<script src="{{url('/template/main')}}/assets/libs/datatable/dataTables.colReorder.min.js"></script>

<script>
    $(document).ready(function() {
    // Handle edit button click
    $('.edit-report-btn').on('click', function() {
        const reportId = $(this).data('report-id');
        const reportName = $(this).data('report-name');
        const reportType = $(this).data('report-type');
        const reportSheetId = $(this).data('report-sheet-id');

        // Populate the edit form
        $('#edit_report_name').val(reportName);
        $('#edit_report_type').val(reportType);
        $('#edit_report_sheet_id').val(reportSheetId);

        // Set the form action URL
        $('#editEcomReportForm').attr('action', `/ecom-reports/${reportId}`);

        // Hide any existing alerts
        $('#editSuccessAlert, #editErrorAlert').addClass('d-none');
    });
    
    // Handle edit form submission
    $('#editEcomReportForm').on('submit', function(e) {
        e.preventDefault();
        
        const form = $(this);
        const formData = new FormData(this);
        const url = form.attr('action');
        
        // Hide any existing alerts
        $('#editSuccessAlert, #editErrorAlert').addClass('d-none');
        
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                // Show success message
                $('#editSuccessMessage').text(response.message || 'Report updated successfully!');
                $('#editSuccessAlert').removeClass('d-none');
                
                // Close modal after 2 seconds
                setTimeout(function() {
                    $('#editEcomReportModal').modal('hide');
                    // Reload the page to show updated data
                    location.reload();
                }, 2000);
            },
            error: function(xhr) {
                let errorMessage = 'An error occurred while updating the report.';
                
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                
                // Show error message
                $('#editErrorMessage').text(errorMessage);
                $('#editErrorAlert').removeClass('d-none');
            }
        });
    });
});
</script>

@endsection