<!-- Cell Configuration Modal -->
<div class="modal fade" id="cellConfigModal" tabindex="-1" aria-labelledby="cellConfigModalLabel" aria-hidden="true" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cellConfigModalLabel">Configure Cell: <span
                        class="badge border border-secondary text-secondary cell-name">-</span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="editingCellIdentifier">

                <div class="mb-3">
                    <label for="cellSheetName" class="form-label">1. Select Source Sheet:</label>
                    <select class="form-select select2-enabled" id="cellSheetName" style="width: 100%;">
                        <option value="">-- Select Sheet --</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="cellMetricColumn" class="form-label">2. Select Metric Column from Sheet:</label>
                    <select class="form-select select2-enabled" id="cellMetricColumn" disabled style="width: 100%;">
                        <option value="">-- Select Column --</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label class="form-label">3. Value Type & Aggregation:</label>
                    <div class="row">
                        <div class="col-md-6">
                            <label for="cellValueType" class="form-label">Value Type:</label>
                            <select class="form-select select2-enabled" id="cellValueType" style="width: 100%;">
                                <option value="number" selected>Number</option>
                                <option value="percent">Percent</option>
                            </select>
                        </div>
                        <div class="col-md-6" id="cellNumberOptionsContainer">
                            <label for="cellDisplayFormat" class="form-label">Number Format:</label>
                            <select class="form-select select2-enabled mb-2" id="cellDisplayFormat"
                                style="width: 100%;">
                                <option value="default" selected>Default</option>
                                <option value="number_0">Number (0 decimal)</option>
                                <option value="number_2">Number (2 decimals)</option>
                                <option value="percent_0">Percent (0 decimal)</option>
                                <option value="percent_2">Percent (2 decimals)</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="cellAggregationType" class="form-label">4. Aggregation Type:</label>
                    <select class="form-select select2-enabled" id="cellAggregationType" style="width: 100%;">
                        <option value="sum" selected>Sum of Values</option>
                        <option value="direct">Direct Value (No Aggregation)</option>
                        <option value="average">Average of Values</option>
                        <option value="first">First Value</option>
                        <option value="last">Last Value</option>
                        <option value="count">Count of Values</option>
                    </select>
                </div>

                <hr>
                <h5>Or, Define Custom Formula</h5>
                <div class="form-check form-switch mb-2">
                    <input class="form-check-input" type="checkbox" role="switch" id="enableCustomFormula">
                    <label class="form-check-label" for="enableCustomFormula">Use Custom Formula for this Cell</label>
                </div>

                <div id="customFormulaBuilder" style="display:none;">
                    <div class="mb-3">
                        <label for="cellFormulaOperandSelect" class="form-label">Available Metrics/Values:</label>
                        <select id="cellFormulaOperandSelect" class="form-select form-select-sm select2-enabled"
                            style="width: 300px; margin-right: 5px;">
                            <option value="">Select Metric/Number</option>
                        </select>
                        <button class="btn btn-secondary btn-sm operator-btn" data-op="+">+</button>
                        <button class="btn btn-secondary btn-sm operator-btn" data-op="-">-</button>
                        <button class="btn btn-secondary btn-sm operator-btn" data-op="*">*</button>
                        <button class="btn btn-secondary btn-sm operator-btn" data-op="/">/</button>
                        <button class="btn btn-secondary btn-sm operator-btn" data-op="(">(</button>
                        <button class="btn btn-secondary btn-sm operator-btn" data-op=")">)</button>
                        <button id="addCellOperandToFormulaBtn" class="btn btn-info btn-sm">Add to Formula</button>
                    </div>
                    <div class="mb-3">
                        <strong>Current Formula:</strong>
                        <div id="currentCellFormulaDisplay" class="border p-2"
                            style="min-height: 40px; background-color: #f8f9fa;"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="saveCellConfigBtn">Save Configuration</button>
            </div>
        </div>
    </div>
</div>