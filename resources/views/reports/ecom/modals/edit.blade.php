<div class="modal fade" id="editEcomReportModal" tabindex="-1" aria-labelledby="editEcomReportModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editEcomReportModalLabel">Edit ECOM Report</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <form id="editEcomReportForm" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <!-- Success Alert -->
                    <div class="alert alert-success d-none" id="editSuccessAlert" role="alert">
                        <i class="ri-check-line me-2"></i>
                        <span id="editSuccessMessage">Report updated successfully!</span>
                    </div>

                    <!-- Error Alert -->
                    <div class="alert alert-danger d-none" id="editErrorAlert" role="alert">
                        <i class="ri-error-warning-line me-2"></i>
                        <span id="editErrorMessage">An error occurred while updating the report.</span>
                    </div>

                    <div class="row g-3">
                        <div class="col-md-12">
                            <label for="edit_report_name" class="form-label">Report Name</label>
                            <input type="text" class="form-control" id="edit_report_name" name="name"
                                placeholder="​CUCKOO - OVERALL - ECOM PLAN​" required>
                        </div>
                        <div class="col-md-12">
                            <label for="edit_report_type" class="form-label">Report Type</label>
                            <select class="form-select" id="edit_report_type" name="type" required>
                                <option value="ecom">E-commerce</option>
                                <option value="tiktok">TikTok</option>
                            </select>
                        </div>
                        <div class="col-md-12">
                            <label for="edit_report_sheet_id" class="form-label">Sheet ID (Shared with:
                                <EMAIL>)</label>
                            <input type="text" class="form-control" id="edit_report_sheet_id" name="sheet_id"
                                placeholder="Ex: 1QpLtVE48CLppEZyLJsuSQyCgCBSU6WLCc4yJ6znaw18" required>
                        </div>
                        <div class="col-md-12">
                            <label for="edit_report_client" class="form-label">Client:</label>
                            <input type="text" class="form-control" id="edit_report_client" name="client" value="CUCKOO"
                                disabled>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>