@extends('layout.ecom.master')
@section('head')
{{-- import css --}}
<link rel="stylesheet" type="text/css" href="{{url('/template/main')}}/assets/css/select2.min.css" />
<link href="{{url('/template/main')}}/assets/css/placeholder-loading.min.css" rel="stylesheet" type="text/css" />
<link rel="stylesheet" type="text/css" href="{{url('/template/main')}}/assets/css/daterangepicker.css" />
<link rel="stylesheet" type="text/css" href="{{url('/template/main')}}/assets/css/chosen.css" />
<link rel="stylesheet" type="text/css" href="{{url('/template/main')}}/assets/css/dataTables.bootstrap5.min.css" />
<link href="{{ asset('css/report/ecom/index.css') }}" rel="stylesheet">
@include('reports.ecom.common.style')
<style>
    /* Select2 Custom Styles for Modal */
    #cellConfigModal .select2-container {
        z-index: 9999 !important;
        width: 100% !important;
    }

    #cellConfigModal .select2-container .select2-selection--single {
        height: 38px !important;
        border: 1px solid #ced4da !important;
        border-radius: 0.375rem !important;
        background-color: #fff !important;
    }

    #cellConfigModal .select2-container .select2-selection--single .select2-selection__rendered {
        line-height: 36px !important;
        padding-left: 12px !important;
        padding-right: 30px !important;
        color: #495057 !important;
    }

    #cellConfigModal .select2-container .select2-selection--single .select2-selection__arrow {
        height: 36px !important;
        position: absolute !important;
        top: 1px !important;
        right: 1px !important;
        width: 20px !important;
    }

    #cellConfigModal .select2-container .select2-selection--single .select2-selection__arrow b {
        border-color: #999 transparent transparent transparent !important;
        border-style: solid !important;
        border-width: 5px 4px 0 4px !important;
        height: 0 !important;
        left: 50% !important;
        margin-left: -4px !important;
        margin-top: -2px !important;
        position: absolute !important;
        top: 50% !important;
        width: 0 !important;
    }

    #cellConfigModal .select2-dropdown {
        border: 1px solid #ced4da !important;
        border-radius: 0.375rem !important;
        background-color: #fff !important;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
    }

    #cellConfigModal .select2-results__option {
        padding: 6px 12px !important;
        color: #495057 !important;
    }

    #cellConfigModal .select2-results__option--highlighted {
        background-color: #007bff !important;
        color: #fff !important;
    }

    #cellConfigModal .select2-results__option--selected {
        background-color: #e9ecef !important;
        color: #495057 !important;
    }

    #cellConfigModal .select2-search--dropdown {
        padding: 4px !important;
        background-color: #fff !important;
    }

    #cellConfigModal .select2-search--dropdown .select2-search__field {
        border: 1px solid #ced4da !important;
        border-radius: 0.25rem !important;
        padding: 4px 8px !important;
        width: 100% !important;
        box-sizing: border-box !important;
    }

    .select2-container--open .select2-dropdown {
        z-index: 9999 !important;
    }

    #cellConfigModal .select2-container--open .select2-selection--single .select2-selection__arrow b {
        border-color: transparent transparent #999 transparent !important;
        border-width: 0 4px 5px 4px !important;
    }

    .page-content {
        padding: 0;
    }

    .report-content {
        margin-bottom: 4rem;
    }
</style>
@endsection

@section('main-content')

<div class="report-container">
    <div class="report-header">
        <div class="reportrange-container">
            <div class="overview-reportrange">
                <div id="reportrange" style="">
                    <i class="fa fa-calendar"></i>&nbsp;
                    <span></span> <i class="fa fa-caret-down"></i>
                </div>
            </div>
        </div>

        <div class="title">CUCKOO - OVERALL - SHOPEE SEM/PMax PLAN</div>
        <div class="logo"><img src="/imgs/clients/cuckoo.jpeg" alt="CUCKOO"></div>
    </div>

    <div class="report-content">
        <div class="timeline ms-4 mt-2 p-0 d-flex gap-2 justify-content-start">
            <div class="timeline-label" style="width: 100px;">%TIMELINE
            </div>
            <div class="timeline-value overall-percentage" style="width: 100px;">-%</div>
        </div>

        <!-- PLAN Section -->
        <div class="table-container">
            <table class="table" id="plan-table" data-name="overall-shopee-sem-pmax" data-id="overall-shopee-sem-pmax">
                <tbody>
                    <tr>
                        <td colspan="9">
                            <div class="group">PLAN</div>
                        </td>
                    </tr>

                    <tr class="header-row">
                        <td colspan="4">
                            <div>SUMMARY METRICS</div>
                        </td>
                        <td colspan="2">
                            <div>COMMITTED KPIs</div>
                        </td>
                        <td colspan="3">
                            <div>ESTIMATE KPIs</div>
                        </td>
                    </tr>

                    <tr class="header-row">
                        <td>
                            <div>CHANNEL</div>
                        </td>
                        <td>
                            <div>PLATFORM</div>
                        </td>
                        <td>
                            <div>TIMELINE</div>
                        </td>
                        <td>
                            <div>FORMAT</div>
                        </td>
                        <td>
                            <div>BUYING UNIT</div>
                        </td>
                        <td>
                            <div>KPIs</div>
                        </td>
                        <td>
                            <div>SPENDING</div>
                        </td>
                        <td>
                            <div>REVENUE</div>
                        </td>
                        <td>
                            <div>ROAS</div>
                        </td>
                    </tr>

                    <!-- SHOPEE Rows -->
                    <tr>
                        <td class="channel-col" rowspan="2">
                            <div>SHOPEE</div>
                        </td>
                        <td rowspan="2">
                            <div class="platform-col">
                                Off-site
                            </div>
                        </td>
                        <td rowspan="2">
                            <div class="timeline-col">
                                OCT
                            </div>
                        </td>
                        <td class="" data-cell-id="shopeeOffsiteGooglePmaxFormat" data-cell-name="FORMAT">
                            <div class="plan-value format-col">Google - PMAX</div>
                        </td>
                        <td class="" data-cell-id="shopeeOffsiteGooglePmaxBuyingUnit" data-cell-name="BUYING UNIT">
                            <div class="plan-value buying-unit-col">ATC</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteGooglePmaxKpis" data-cell-name="KPIS">
                            <div class="plan-value shopeeOffsiteGooglePmaxKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteGooglePmaxSpending"
                            data-cell-name="SPENDING">
                            <div class="plan-value shopeeOffsiteGooglePmaxSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteGooglePmaxRevenue"
                            data-cell-name="REVENUE">
                            <div class="plan-value shopeeOffsiteGooglePmaxRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteGooglePmaxRoas" data-cell-name="ROAS">
                            <div class="plan-value shopeeOffsiteGooglePmaxRoas"></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="" data-cell-id="shopeeOffsiteGoogleSemFormat" data-cell-name="FORMAT">
                            <div class="plan-value format-col">Google - SEM</div>
                        </td>
                        <td class="" data-cell-id="shopeeOffsiteGoogleSemBuyingUnit" data-cell-name="BUYING UNIT">
                            <div class="plan-value buying-unit-col">ATC</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteGoogleSemKpis" data-cell-name="KPIS">
                            <div class="plan-value shopeeOffsiteGoogleSemKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteGoogleSemSpending"
                            data-cell-name="SPENDING">
                            <div class="plan-value shopeeOffsiteGoogleSemSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteGoogleSemRevenue"
                            data-cell-name="REVENUE">
                            <div class="plan-value shopeeOffsiteGoogleSemRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteGoogleSemRoas" data-cell-name="ROAS">
                            <div class="plan-value shopeeOffsiteGoogleSemRoas"></div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- ACTUAL Section -->
        <div class="table-container">
            <table class="table" id="actual-table" data-name="overall-shopee-sem-pmax"
                data-id="overall-shopee-sem-pmax">
                <tbody>
                    <tr>
                        <td colspan="9">
                            <div class="group">ACTUAL</div>
                        </td>
                    </tr>

                    <tr class="header-row">
                        <td colspan="4">
                            <div>SUMMARY METRICS</div>
                        </td>
                        <td colspan="2">
                            <div>COMMITTED KPIs</div>
                        </td>
                        <td colspan="3">
                            <div>ESTIMATE KPIs</div>
                        </td>
                    </tr>

                    <tr class="header-row">
                        <td>
                            <div>CHANNEL</div>
                        </td>
                        <td>
                            <div>PLATFORM</div>
                        </td>
                        <td>
                            <div>TIMELINE</div>
                        </td>
                        <td>
                            <div>FORMAT</div>
                        </td>
                        <td>
                            <div>BUYING UNIT</div>
                        </td>
                        <td>
                            <div>KPIs</div>
                        </td>
                        <td>
                            <div>SPENDING</div>
                        </td>
                        <td>
                            <div>REVENUE</div>
                        </td>
                        <td>
                            <div>ROAS</div>
                        </td>
                    </tr>

                    <!-- SHOPEE Actual Rows -->
                    <tr>
                        <td class="channel-col" rowspan="2">
                            <div>SHOPEE</div>
                        </td>
                        <td rowspan="2">
                            <div class="platform-col">
                                Off-site
                            </div>
                        </td>
                        <td rowspan="2">
                            <div class="timeline-col">
                                OCT
                            </div>
                        </td>
                        <td class="" data-cell-id="shopeeActualOffsiteGooglePmaxFormat" data-cell-name="FORMAT">
                            <div class="format-col">Google - PMAX</div>
                        </td>
                        <td class="" data-cell-id="shopeeActualOffsiteGooglePmaxBuyingUnit"
                            data-cell-name="BUYING UNIT">
                            <div class="buying-unit-col">ATC</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteGooglePmaxKpis"
                            data-cell-name="KPIS">
                            <div class="actual-value shopeeActualOffsiteGooglePmaxKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteGooglePmaxSpending"
                            data-cell-name="SPENDING">
                            <div class="actual-value shopeeActualOffsiteGooglePmaxSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteGooglePmaxRevenue"
                            data-cell-name="REVENUE">
                            <div class="actual-value shopeeActualOffsiteGooglePmaxRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteGooglePmaxRoas"
                            data-cell-name="ROAS">
                            <div class="actual-value shopeeActualOffsiteGooglePmaxRoas"></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="" data-cell-id="shopeeActualOffsiteGoogleSemFormat" data-cell-name="FORMAT">
                            <div class="format-col">Google - SEM</div>
                        </td>
                        <td class="" data-cell-id="shopeeActualOffsiteGoogleSemBuyingUnit" data-cell-name="BUYING UNIT">
                            <div class="buying-unit-col">ATC</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteGoogleSemKpis"
                            data-cell-name="KPIS">
                            <div class="actual-value shopeeActualOffsiteGoogleSemKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteGoogleSemSpending"
                            data-cell-name="SPENDING">
                            <div class="actual-value shopeeActualOffsiteGoogleSemSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteGoogleSemRevenue"
                            data-cell-name="REVENUE">
                            <div class="actual-value shopeeActualOffsiteGoogleSemRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteGoogleSemRoas"
                            data-cell-name="ROAS">
                            <div class="actual-value shopeeActualOffsiteGoogleSemRoas"></div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- ACHIEVEMENT Section -->
        <div class="table-container">
            <table class="table" id="achievement-table" data-name="overall-shopee-sem-pmax"
                data-id="overall-shopee-sem-pmax">
                <tbody>
                    <tr>
                        <td colspan="9">
                            <div class="group">% ACHIEVEMENT</div>
                        </td>
                    </tr>

                    <tr class="header-row">
                        <td colspan="4">
                            <div>SUMMARY METRICS</div>
                        </td>
                        <td colspan="2">
                            <div>COMMITTED KPIs</div>
                        </td>
                        <td colspan="3">
                            <div>ESTIMATE KPIs</div>
                        </td>
                    </tr>

                    <tr class="header-row">
                        <td>
                            <div>CHANNEL</div>
                        </td>
                        <td>
                            <div>PLATFORM</div>
                        </td>
                        <td>
                            <div>TIMELINE</div>
                        </td>
                        <td>
                            <div>FORMAT</div>
                        </td>
                        <td>
                            <div>BUYING UNIT</div>
                        </td>
                        <td>
                            <div>KPIs</div>
                        </td>
                        <td>
                            <div>SPENDING</div>
                        </td>
                        <td>
                            <div>REVENUE</div>
                        </td>
                        <td>
                            <div>ROAS</div>
                        </td>
                    </tr>

                    <!-- SHOPEE Achievement Rows -->
                    <tr>
                        <td class="channel-col" rowspan="2">
                            <div>SHOPEE</div>
                        </td>
                        <td rowspan="2">
                            <div class="platform-col">
                                Off-site
                            </div>
                        </td>
                        <td rowspan="2">
                            <div class="timeline-col">
                                OCT
                            </div>
                        </td>
                        <td class="" data-cell-id="shopeeAchievementOffsiteGooglePmaxFormat" data-cell-name="FORMAT">
                            <div class="format-col">Google - PMAX</div>
                        </td>
                        <td class="" data-cell-id="shopeeAchievementOffsiteGooglePmaxBuyingUnit"
                            data-cell-name="BUYING UNIT">
                            <div class="buying-unit-col">ATC</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOffsiteGooglePmaxKpis"
                            data-cell-name="KPIS">
                            <div class="achievement-value shopeeAchievementOffsiteGooglePmaxKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOffsiteGooglePmaxSpending"
                            data-cell-name="SPENDING">
                            <div class="achievement-value shopeeAchievementOffsiteGooglePmaxSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOffsiteGooglePmaxRevenue"
                            data-cell-name="REVENUE">
                            <div class="achievement-value shopeeAchievementOffsiteGooglePmaxRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOffsiteGooglePmaxRoas"
                            data-cell-name="ROAS">
                            <div class="achievement-value shopeeAchievementOffsiteGooglePmaxRoas"></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="" data-cell-id="shopeeAchievementOffsiteGoogleSemFormat" data-cell-name="FORMAT">
                            <div class="format-col">Google - SEM</div>
                        </td>
                        <td class="" data-cell-id="shopeeAchievementOffsiteGoogleSemBuyingUnit"
                            data-cell-name="BUYING UNIT">
                            <div class="buying-unit-col">ATC</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOffsiteGoogleSemKpis"
                            data-cell-name="KPIS">
                            <div class="achievement-value shopeeAchievementOffsiteGoogleSemKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOffsiteGoogleSemSpending"
                            data-cell-name="SPENDING">
                            <div class="achievement-value shopeeAchievementOffsiteGoogleSemSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOffsiteGoogleSemRevenue"
                            data-cell-name="REVENUE">
                            <div class="achievement-value shopeeAchievementOffsiteGoogleSemRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOffsiteGoogleSemRoas"
                            data-cell-name="ROAS">
                            <div class="achievement-value shopeeAchievementOffsiteGoogleSemRoas"></div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

@if (!$view)
@include('reports.ecom.modals.cell-config-modal')
@endif

@endsection
@section('script')

@include('reports.ecom.common.script')

@endsection