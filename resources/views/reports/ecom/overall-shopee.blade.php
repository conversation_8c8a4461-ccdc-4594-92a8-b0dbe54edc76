@extends('layout.ecom.master')
@section('head')
{{-- import css --}}
<link rel="stylesheet" type="text/css" href="{{url('/template/main')}}/assets/css/select2.min.css" />
<link href="{{url('/template/main')}}/assets/css/placeholder-loading.min.css" rel="stylesheet" type="text/css" />
<link rel="stylesheet" type="text/css" href="{{url('/template/main')}}/assets/css/daterangepicker.css" />
<link rel="stylesheet" type="text/css" href="{{url('/template/main')}}/assets/css/chosen.css" />
<link rel="stylesheet" type="text/css" href="{{url('/template/main')}}/assets/css/dataTables.bootstrap5.min.css" />
<link href="{{ asset('css/report/ecom/index.css') }}" rel="stylesheet">
@include('reports.ecom.common.style')
<style>
    /* Select2 Custom Styles for Modal */
    #cellConfigModal .select2-container {
        z-index: 9999 !important;
        width: 100% !important;
    }

    #cellConfigModal .select2-container .select2-selection--single {
        height: 38px !important;
        border: 1px solid #ced4da !important;
        border-radius: 0.375rem !important;
        background-color: #fff !important;
    }

    #cellConfigModal .select2-container .select2-selection--single .select2-selection__rendered {
        line-height: 36px !important;
        padding-left: 12px !important;
        padding-right: 30px !important;
        color: #495057 !important;
    }

    #cellConfigModal .select2-container .select2-selection--single .select2-selection__arrow {
        height: 36px !important;
        position: absolute !important;
        top: 1px !important;
        right: 1px !important;
        width: 20px !important;
    }

    #cellConfigModal .select2-container .select2-selection--single .select2-selection__arrow b {
        border-color: #999 transparent transparent transparent !important;
        border-style: solid !important;
        border-width: 5px 4px 0 4px !important;
        height: 0 !important;
        left: 50% !important;
        margin-left: -4px !important;
        margin-top: -2px !important;
        position: absolute !important;
        top: 50% !important;
        width: 0 !important;
    }

    #cellConfigModal .select2-dropdown {
        border: 1px solid #ced4da !important;
        border-radius: 0.375rem !important;
        background-color: #fff !important;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
    }

    #cellConfigModal .select2-results__option {
        padding: 6px 12px !important;
        color: #495057 !important;
    }

    #cellConfigModal .select2-results__option--highlighted {
        background-color: #007bff !important;
        color: #fff !important;
    }

    #cellConfigModal .select2-results__option--selected {
        background-color: #e9ecef !important;
        color: #495057 !important;
    }

    #cellConfigModal .select2-search--dropdown {
        padding: 4px !important;
        background-color: #fff !important;
    }

    #cellConfigModal .select2-search--dropdown .select2-search__field {
        border: 1px solid #ced4da !important;
        border-radius: 0.25rem !important;
        padding: 4px 8px !important;
        width: 100% !important;
        box-sizing: border-box !important;
    }

    .select2-container--open .select2-dropdown {
        z-index: 9999 !important;
    }

    #cellConfigModal .select2-container--open .select2-selection--single .select2-selection__arrow b {
        border-color: transparent transparent #999 transparent !important;
        border-width: 0 4px 5px 4px !important;
    }

    .page-content {
        padding: 0;
    }

    .report-content {
        margin-bottom: 4rem;
    }
</style>
@endsection

@section('main-content')

<div class="report-container">
    <div class="report-header">
        <div class="reportrange-container">
            <div class="overview-reportrange">
                <div id="reportrange" style="">
                    <i class="fa fa-calendar"></i>&nbsp;
                    <span></span> <i class="fa fa-caret-down"></i>
                </div>
            </div>
        </div>

        <div class="title">CUCKOO - OVERALL - SHOPEE PLAN</div>
        <div class="logo"><img src="/imgs/clients/cuckoo.jpeg" alt="CUCKOO"></div>
    </div>

    <div class="report-content">
        <div class="timeline ms-4 mt-2 p-0 d-flex gap-2 justify-content-start">
            <div class="timeline-label" style="width: 100px;">%TIMELINE
            </div>
            <div class="timeline-value overall-percentage" style="width: 100px;">-%</div>
        </div>

        <!-- PLAN Section -->
        <div class="table-container">
            <table class="table" id="plan-table" data-name="overall-shopee" data-id="overall-shopee">
                <tbody>
                    <tr>
                        <td colspan="9">
                            <div class="group">PLAN</div>
                        </td>
                    </tr>

                    <tr class="header-row">
                        <td colspan="4">
                            <div>SUMMARY METRICS</div>
                        </td>
                        <td colspan="2">
                            <div>COMMITTED KPIs</div>
                        </td>
                        <td colspan="3">
                            <div>ESTIMATE KPIs</div>
                        </td>
                    </tr>

                    <tr class="header-row">
                        <td>
                            <div>CHANNEL</div>
                        </td>
                        <td>
                            <div>PLATFORM</div>
                        </td>
                        <td>
                            <div>TIMELINE</div>
                        </td>
                        <td>
                            <div>FORMAT</div>
                        </td>
                        <td>
                            <div>BUYING UNIT</div>
                        </td>
                        <td>
                            <div>KPIs</div>
                        </td>
                        <td>
                            <div>SPENDING</div>
                        </td>
                        <td>
                            <div>REVENUE</div>
                        </td>
                        <td>
                            <div>ROAS</div>
                        </td>
                    </tr>

                    <!-- SHOPEE Rows -->
                    <tr>
                        <td class="channel-col" rowspan="8">
                            <div>SHOPEE</div>
                        </td>
                        <td rowspan="4">
                            <div class="platform-col">
                                Off-site
                            </div>
                        </td>
                        <td rowspan="8">
                            <div class="timeline-col">
                                OCT
                            </div>
                        </td>
                        <td class="" data-cell-id="shopeeOffsiteFacebookConversionFormat" data-cell-name="FORMAT">
                            <div class="plan-value format-col">Facebook - Conversion</div>
                        </td>
                        <td class="" data-cell-id="shopeeOffsiteFacebookConversionBuyingUnit"
                            data-cell-name="BUYING UNIT">
                            <div class="plan-value buying-unit-col">ATC</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteFacebookConversionKpis"
                            data-cell-name="KPIS">
                            <div class="plan-value shopeeOffsiteFacebookConversionKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteFacebookConversionSpending"
                            data-cell-name="SPENDING">
                            <div class="plan-value shopeeOffsiteFacebookConversionSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteFacebookConversionRevenue"
                            data-cell-name="REVENUE">
                            <div class="plan-value shopeeOffsiteFacebookConversionRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteFacebookConversionRoas"
                            data-cell-name="ROAS">
                            <div class="plan-value shopeeOffsiteFacebookConversionRoas"></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="" data-cell-id="shopeeOffsiteFacebookCpasFormat" data-cell-name="FORMAT">
                            <div class="plan-value format-col">Facebook - CPAS</div>
                        </td>
                        <td class="" data-cell-id="shopeeOffsiteFacebookCpasBuyingUnit" data-cell-name="BUYING UNIT">
                            <div class="plan-value buying-unit-col">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteFacebookCpasKpis"
                            data-cell-name="KPIS">
                            <div class="plan-value shopeeOffsiteFacebookCpasKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteFacebookCpasSpending"
                            data-cell-name="SPENDING">
                            <div class="plan-value shopeeOffsiteFacebookCpasSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteFacebookCpasRevenue"
                            data-cell-name="REVENUE">
                            <div class="plan-value shopeeOffsiteFacebookCpasRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteFacebookCpasRoas"
                            data-cell-name="ROAS">
                            <div class="plan-value shopeeOffsiteFacebookCpasRoas"></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="" data-cell-id="shopeeOffsiteGoogleSemFormat" data-cell-name="FORMAT">
                            <div class="plan-value format-col">Google SEM</div>
                        </td>
                        <td class="" data-cell-id="shopeeOffsiteGoogleSemBuyingUnit" data-cell-name="BUYING UNIT">
                            <div class="plan-value buying-unit-col">ATC</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteGoogleSemKpis" data-cell-name="KPIS">
                            <div class="plan-value shopeeOffsiteGoogleSemKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteGoogleSemSpending"
                            data-cell-name="SPENDING">
                            <div class="plan-value shopeeOffsiteGoogleSemSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteGoogleSemRevenue"
                            data-cell-name="REVENUE">
                            <div class="plan-value shopeeOffsiteGoogleSemRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteGoogleSemRoas" data-cell-name="ROAS">
                            <div class="plan-value shopeeOffsiteGoogleSemRoas"></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="" data-cell-id="shopeeOffsiteYoutubeTrueviewActionFormat" data-cell-name="FORMAT">
                            <div class="plan-value format-col">Youtube TrueView Action</div>
                        </td>
                        <td class="" data-cell-id="shopeeOffsiteYoutubeTrueviewActionBuyingUnit"
                            data-cell-name="BUYING UNIT">
                            <div class="plan-value buying-unit-col">Click</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteYoutubeTrueviewActionKpis"
                            data-cell-name="KPIS">
                            <div class="plan-value shopeeOffsiteGoogleSemKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteYoutubeTrueviewActionSpending"
                            data-cell-name="SPENDING">
                            <div class="plan-value shopeeOffsiteYoutubeTrueviewActionSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteYoutubeTrueviewActionRevenue"
                            data-cell-name="REVENUE">
                            <div class="plan-value shopeeOffsiteGoogleSemRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOffsiteYoutubeTrueviewActionRoas"
                            data-cell-name="ROAS">
                            <div class="plan-value shopeeOffsiteYoutubeTrueviewActionRoas"></div>
                        </td>
                    </tr>

                    <tr>
                        <td rowspan="3">
                            <div class="platform-col">
                                On-site
                            </div>
                        </td>
                        <td class="" data-cell-id="shopeeOnsiteSearchDiscoveryFormat" data-cell-name="FORMAT">
                            <div class="plan-value format-col">Search/Discovery</div>
                        </td>
                        <td class="" data-cell-id="shopeeOnsiteSearchDiscoveryBuyingUnit" data-cell-name="BUYING UNIT">
                            <div class="plan-value buying-unit-col">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOnsiteSearchDiscoveryKpis"
                            data-cell-name="KPIS">
                            <div class="plan-value shopeeOnsiteSearchDiscoveryKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOnsiteSearchDiscoverySpending"
                            data-cell-name="SPENDING">
                            <div class="plan-value shopeeOnsiteSearchDiscoverySpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOnsiteSearchDiscoveryRevenue"
                            data-cell-name="REVENUE">
                            <div class="plan-value shopeeOnsiteSearchDiscoveryRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOnsiteSearchDiscoveryRoas"
                            data-cell-name="ROAS">
                            <div class="plan-value shopeeOnsiteSearchDiscoveryRoas"></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="" data-cell-id="shopeeOnsiteAffiliateFormat" data-cell-name="FORMAT">
                            <div class="plan-value format-col">Affiliate</div>
                        </td>
                        <td class="" data-cell-id="shopeeOnsiteAffiliateBuyingUnit" data-cell-name="BUYING UNIT">
                            <div class="plan-value buying-unit-col">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOnsiteAffiliateKpis" data-cell-name="KPIS">
                            <div class="plan-value shopeeOnsiteAffiliateKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOnsiteAffiliateSpending"
                            data-cell-name="SPENDING">
                            <div class="plan-value shopeeOnsiteAffiliateSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOnsiteAffiliateRevenue"
                            data-cell-name="REVENUE">
                            <div class="plan-value shopeeOnsiteAffiliateRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOnsiteAffiliateRoas" data-cell-name="ROAS">
                            <div class="plan-value shopeeOnsiteAffiliateRoas"></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="" data-cell-id="shopeeOnsiteSponsorMediaFormat" data-cell-name="FORMAT">
                            <div class="plan-value format-col">Sponsor Media</div>
                        </td>
                        <td class="" data-cell-id="shopeeOnsiteSponsorMediaBuyingUnit" data-cell-name="BUYING UNIT">
                            <div class="plan-value buying-unit-col">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOnsiteSponsorMediaKpis" data-cell-name="KPIS">
                            <div class="plan-value shopeeOnsiteSponsorMediaKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOnsiteSponsorMediaSpending"
                            data-cell-name="SPENDING">
                            <div class="plan-value shopeeOnsiteSponsorMediaSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOnsiteSponsorMediaRevenue"
                            data-cell-name="REVENUE">
                            <div class="plan-value shopeeOnsiteSponsorMediaRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOnsiteSponsorMediaRoas" data-cell-name="ROAS">
                            <div class="plan-value shopeeOnsiteSponsorMediaRoas"></div>
                        </td>
                    </tr>

                    <tr>
                        <td rowspan="1">
                            <div class="platform-col">
                                Organic
                            </div>
                        </td>
                        <td class="" data-cell-id="shopeeOrganicOtherFormat" data-cell-name="FORMAT">
                            <div class="plan-value format-col">Other</div>
                        </td>
                        <td class="" data-cell-id="shopeeOrganicOtherBuyingUnit" data-cell-name="BUYING UNIT">
                            <div class="plan-value buying-unit-col">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOrganicOtherKpis" data-cell-name="KPIS">
                            <div class="plan-value shopeeOrganicOtherKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOrganicOtherSpending"
                            data-cell-name="SPENDING">
                            <div class="plan-value shopeeOrganicOtherSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOrganicOtherRevenue" data-cell-name="REVENUE">
                            <div class="plan-value shopeeOrganicOtherRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeOrganicOtherRoas" data-cell-name="ROAS">
                            <div class="plan-value shopeeOrganicOtherRoas"></div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- ACTUAL Section -->
        <div class="table-container">
            <table class="table" id="actual-table" data-name="overall-shopee" data-id="overall-shopee">
                <tbody>
                    <tr>
                        <td colspan="9">
                            <div class="group">ACTUAL</div>
                        </td>
                    </tr>

                    <tr class="header-row">
                        <td colspan="4">
                            <div>SUMMARY METRICS</div>
                        </td>
                        <td colspan="2">
                            <div>COMMITTED KPIs</div>
                        </td>
                        <td colspan="3">
                            <div>ESTIMATE KPIs</div>
                        </td>
                    </tr>

                    <tr class="header-row">
                        <td>
                            <div>CHANNEL</div>
                        </td>
                        <td>
                            <div>PLATFORM</div>
                        </td>
                        <td>
                            <div>TIMELINE</div>
                        </td>
                        <td>
                            <div>FORMAT</div>
                        </td>
                        <td>
                            <div>BUYING UNIT</div>
                        </td>
                        <td>
                            <div>KPIs</div>
                        </td>
                        <td>
                            <div>SPENDING</div>
                        </td>
                        <td>
                            <div>REVENUE</div>
                        </td>
                        <td>
                            <div>ROAS</div>
                        </td>
                    </tr>

                    <!-- SHOPEE Actual Rows -->
                    <tr>
                        <td class="channel-col" rowspan="8">
                            <div>SHOPEE</div>
                        </td>
                        <td rowspan="4">
                            <div class="platform-col">
                                Off-site
                            </div>
                        </td>
                        <td rowspan="8">
                            <div class="timeline-col">
                                OCT
                            </div>
                        </td>
                        <td class="" data-cell-id="shopeeActualOffsiteFacebookConversionFormat" data-cell-name="FORMAT">
                            <div class="format-col">Facebook - Conversion</div>
                        </td>
                        <td class="" data-cell-id="shopeeActualOffsiteFacebookConversionBuyingUnit"
                            data-cell-name="BUYING UNIT">
                            <div class="buying-unit-col">ATC</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteFacebookConversionKpis"
                            data-cell-name="KPIS">
                            <div class="actual-value shopeeActualOffsiteFacebookConversionKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteFacebookConversionSpending"
                            data-cell-name="SPENDING">
                            <div class="actual-value shopeeActualOffsiteFacebookConversionSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteFacebookConversionRevenue"
                            data-cell-name="REVENUE">
                            <div class="actual-value shopeeActualOffsiteFacebookConversionRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteFacebookConversionRoas"
                            data-cell-name="ROAS">
                            <div class="actual-value shopeeActualOffsiteFacebookConversionRoas"></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="" data-cell-id="shopeeActualOffsiteFacebookCpasFormat">
                            <div class="format-col">Facebook - CPAS</div>
                        </td>
                        <td class="" data-cell-id="shopeeActualOffsiteFacebookCpasBuyingUnit"
                            data-cell-name="BUYING UNIT">
                            <div class="buying-unit-col">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteFacebookCpasKpis"
                            data-cell-name="KPIS">
                            <div class="actual-value shopeeActualOffsiteFacebookCpasKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteFacebookCpasSpending"
                            data-cell-name="SPENDING">
                            <div class="actual-value shopeeActualOffsiteFacebookCpasSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteFacebookCpasRevenue"
                            data-cell-name="REVENUE">
                            <div class="actual-value shopeeActualOffsiteFacebookCpasRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteFacebookCpasRoas"
                            data-cell-name="ROAS">
                            <div class="actual-value shopeeActualOffsiteFacebookCpasRoas"></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="" data-cell-id="shopeeActualOffsiteGoogleSemFormat" data-cell-name="FORMAT">
                            <div class="format-col">Google SEM</div>
                        </td>
                        <td class="" data-cell-id="shopeeActualOffsiteGoogleSemBuyingUnit" data-cell-name="BUYING UNIT">
                            <div class="buying-unit-col">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteGoogleSemKpis"
                            data-cell-name="KPIS">
                            <div class="actual-value shopeeActualOffsiteGoogleSemKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteGoogleSemSpending"
                            data-cell-name="SPENDING">
                            <div class="actual-value shopeeActualOffsiteGoogleSemSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteGoogleSemRevenue"
                            data-cell-name="REVENUE">
                            <div class="actual-value shopeeActualOffsiteGoogleSemRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteGoogleSemRoas"
                            data-cell-name="ROAS">
                            <div class="actual-value shopeeActualOffsiteGoogleSemRoas"></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="" data-cell-id="shopeeActualOffsiteYoutubeTrueviewActionFormat"
                            data-cell-name="FORMAT">
                            <div class="plan-value format-col">Youtube TrueView Action</div>
                        </td>
                        <td class="" data-cell-id="shopeeActualOffsiteYoutubeTrueviewActionBuyingUnit"
                            data-cell-name="BUYING UNIT">
                            <div class="plan-value buying-unit-col">Click</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteYoutubeTrueviewActionKpis"
                            data-cell-name="KPIS">
                            <div class="actual-value shopeeActualOffsiteYoutubeTrueviewActionKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteYoutubeTrueviewActionSpending"
                            data-cell-name="SPENDING">
                            <div class="actual-value shopeeActualOffsiteYoutubeTrueviewActionSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteYoutubeTrueviewActionRevenue"
                            data-cell-name="REVENUE">
                            <div class="actual-value shopeeActualOffsiteYoutubeTrueviewActionRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOffsiteYoutubeTrueviewActionRoas"
                            data-cell-name="ROAS">
                            <div class="actual-value shopeeActualOffsiteYoutubeTrueviewActionRoas"></div>
                        </td>
                    </tr>

                    <tr>
                        <td rowspan="3">
                            <div class="platform-col">
                                On-site
                            </div>
                        </td>
                        <td class="" data-cell-id="shopeeActualOnsiteSearchDiscoveryFormat">
                            <div class="format-col">Search/Discovery</div>
                        </td>
                        <td class="" data-cell-id="shopeeActualOnsiteSearchDiscoveryBuyingUnit"
                            data-cell-name="BUYING UNIT">
                            <div class="buying-unit-col">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOnsiteSearchDiscoveryKpis"
                            data-cell-name="KPIS">
                            <div class="actual-value shopeeActualOnsiteSearchDiscoveryKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOnsiteSearchDiscoverySpending"
                            data-cell-name="SPENDING">
                            <div class="actual-value shopeeActualOnsiteSearchDiscoverySpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOnsiteSearchDiscoveryRevenue"
                            data-cell-name="REVENUE">
                            <div class="actual-value shopeeActualOnsiteSearchDiscoveryRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOnsiteSearchDiscoveryRoas"
                            data-cell-name="ROAS">
                            <div class="actual-value shopeeActualOnsiteSearchDiscoveryRoas"></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="" data-cell-id="shopeeActualOnsiteAffiliateFormat" data-cell-name="FORMAT">
                            <div class="format-col">Affiliate</div>
                        </td>
                        <td class="" data-cell-id="shopeeActualOnsiteAffiliateBuyingUnit" data-cell-name="BUYING UNIT">
                            <div class="buying-unit-col">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOnsiteAffiliateKpis"
                            data-cell-name="KPIS">
                            <div class="actual-value shopeeActualOnsiteAffiliateKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOnsiteAffiliateSpending"
                            data-cell-name="SPENDING">
                            <div class="actual-value shopeeActualOnsiteAffiliateSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOnsiteAffiliateRevenue"
                            data-cell-name="REVENUE">
                            <div class="actual-value shopeeActualOnsiteAffiliateRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOnsiteAffiliateRoas"
                            data-cell-name="ROAS">
                            <div class="actual-value shopeeActualOnsiteAffiliateRoas"></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="" data-cell-id="shopeeActualOnsiteSponsorMediaFormat" data-cell-name="FORMAT">
                            <div class="format-col">Sponsor Media</div>
                        </td>
                        <td class="" data-cell-id="shopeeActualOnsiteSponsorMediaBuyingUnit"
                            data-cell-name="BUYING UNIT">
                            <div class="buying-unit-col">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOnsiteSponsorMediaKpis"
                            data-cell-name="KPIS">
                            <div class="actual-value shopeeActualOnsiteSponsorMediaKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOnsiteSponsorMediaSpending"
                            data-cell-name="SPENDING">
                            <div class="actual-value shopeeActualOnsiteSponsorMediaSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOnsiteSponsorMediaRevenue"
                            data-cell-name="REVENUE">
                            <div class="actual-value shopeeActualOnsiteSponsorMediaRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOnsiteSponsorMediaRoas"
                            data-cell-name="ROAS">
                            <div class="actual-value shopeeActualOnsiteSponsorMediaRoas"></div>
                        </td>
                    </tr>

                    <tr>
                        <td rowspan="1">
                            <div class="platform-col">
                                Organic
                            </div>
                        </td>
                        <td class="" data-cell-id="shopeeActualOrganicOtherFormat" data-cell-name="FORMAT">
                            <div class="format-col">Other</div>
                        </td>
                        <td class="" data-cell-id="shopeeActualOrganicOtherBuyingUnit" data-cell-name="BUYING UNIT">
                            <div class="buying-unit-col">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOrganicOtherKpis" data-cell-name="KPIS">
                            <div class="actual-value shopeeActualOrganicOtherKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOrganicOtherSpending"
                            data-cell-name="SPENDING">
                            <div class="actual-value shopeeActualOrganicOtherSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOrganicOtherRevenue"
                            data-cell-name="REVENUE">
                            <div class="actual-value shopeeActualOrganicOtherRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualOrganicOtherRoas" data-cell-name="ROAS">
                            <div class="actual-value shopeeActualOrganicOtherRoas"></div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- ACHIEVEMENT Section -->
        <div class="table-container">
            <table class="table" id="achievement-table" data-name="overall-shopee" data-id="overall-shopee">
                <tbody>
                    <tr>
                        <td colspan="9">
                            <div class="group">% ACHIEVEMENT</div>
                        </td>
                    </tr>

                    <tr class="header-row">
                        <td colspan="4">
                            <div>SUMMARY METRICS</div>
                        </td>
                        <td colspan="2">
                            <div>COMMITTED KPIs</div>
                        </td>
                        <td colspan="3">
                            <div>ESTIMATE KPIs</div>
                        </td>
                    </tr>

                    <tr class="header-row">
                        <td>
                            <div>CHANNEL</div>
                        </td>
                        <td>
                            <div>PLATFORM</div>
                        </td>
                        <td>
                            <div>TIMELINE</div>
                        </td>
                        <td>
                            <div>FORMAT</div>
                        </td>
                        <td>
                            <div>BUYING UNIT</div>
                        </td>
                        <td>
                            <div>KPIs</div>
                        </td>
                        <td>
                            <div>SPENDING</div>
                        </td>
                        <td>
                            <div>REVENUE</div>
                        </td>
                        <td>
                            <div>ROAS</div>
                        </td>
                    </tr>

                    <!-- SHOPEE Achievement Rows -->
                    <tr>
                        <td class="channel-col" rowspan="7">
                            <div>SHOPEE</div>
                        </td>
                        <td rowspan="3">
                            <div class="platform-col">
                                Off-site
                            </div>
                        </td>
                        <td rowspan="7">
                            <div class="timeline-col">
                                OCT
                            </div>
                        </td>
                        <td class="" data-cell-id="shopeeAchievementOffsiteFacebookConversionFormat"
                            data-cell-name="FORMAT">
                            <div class="format-col">Facebook - Conversion</div>
                        </td>
                        <td class="" data-cell-id="shopeeAchievementOffsiteFacebookConversionBuyingUnit"
                            data-cell-name="BUYING UNIT">
                            <div class="buying-unit-col">ATC</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOffsiteFacebookConversionKpis"
                            data-cell-name="KPIS">
                            <div class="achievement-value shopeeAchievementOffsiteFacebookConversionKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOffsiteFacebookConversionSpending"
                            data-cell-name="SPENDING">
                            <div class="achievement-value shopeeAchievementOffsiteFacebookConversionSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOffsiteFacebookConversionRevenue"
                            data-cell-name="REVENUE">
                            <div class="achievement-value shopeeAchievementOffsiteFacebookConversionRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOffsiteFacebookConversionRoas"
                            data-cell-name="ROAS">
                            <div class="achievement-value shopeeAchievementOffsiteFacebookConversionRoas"></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="" data-cell-id="shopeeAchievementOffsiteFacebookCpasFormat" data-cell-name="FORMAT">
                            <div class="format-col">Facebook - CPAS</div>
                        </td>
                        <td class="" data-cell-id="shopeeAchievementOffsiteFacebookCpasBuyingUnit"
                            data-cell-name="BUYING UNIT">
                            <div class="buying-unit-col">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOffsiteFacebookCpasKpis"
                            data-cell-name="KPIS">
                            <div class="achievement-value shopeeAchievementOffsiteFacebookCpasKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOffsiteFacebookCpasSpending"
                            data-cell-name="SPENDING">
                            <div class="achievement-value shopeeAchievementOffsiteFacebookCpasSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOffsiteFacebookCpasRevenue"
                            data-cell-name="REVENUE">
                            <div class="achievement-value shopeeAchievementOffsiteFacebookCpasRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOffsiteFacebookCpasRoas"
                            data-cell-name="ROAS">
                            <div class="achievement-value shopeeAchievementOffsiteFacebookCpasRoas"></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="" data-cell-id="shopeeAchievementOffsiteGoogleSemFormat" data-cell-name="FORMAT">
                            <div class="format-col">Google SEM</div>
                        </td>
                        <td class="" data-cell-id="shopeeAchievementOffsiteGoogleSemBuyingUnit"
                            data-cell-name="BUYING UNIT">
                            <div class="buying-unit-col">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOffsiteGoogleSemKpis"
                            data-cell-name="KPIS">
                            <div class="achievement-value shopeeAchievementOffsiteGoogleSemKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOffsiteGoogleSemSpending"
                            data-cell-name="SPENDING">
                            <div class="achievement-value shopeeAchievementOffsiteGoogleSemSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOffsiteGoogleSemRevenue"
                            data-cell-name="REVENUE">
                            <div class="achievement-value shopeeAchievementOffsiteGoogleSemRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOffsiteGoogleSemRoas"
                            data-cell-name="ROAS">
                            <div class="achievement-value shopeeAchievementOffsiteGoogleSemRoas"></div>
                        </td>
                    </tr>

                    <tr>
                        <td rowspan="3">
                            <div class="platform-col">
                                On-site
                            </div>
                        </td>
                        <td class="" data-cell-id="shopeeAchievementOnsiteSearchDiscoveryFormat"
                            data-cell-name="FORMAT">
                            <div class="format-col">Search/Discovery</div>
                        </td>
                        <td class="" data-cell-id="shopeeAchievementOnsiteSearchDiscoveryBuyingUnit"
                            data-cell-name="BUYING UNIT">
                            <div class="buying-unit-col">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOnsiteSearchDiscoveryKpis"
                            data-cell-name="KPIS">
                            <div class="achievement-value shopeeAchievementOnsiteSearchDiscoveryKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOnsiteSearchDiscoverySpending"
                            data-cell-name="SPENDING">
                            <div class="achievement-value shopeeAchievementOnsiteSearchDiscoverySpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOnsiteSearchDiscoveryRevenue"
                            data-cell-name="REVENUE">
                            <div class="achievement-value shopeeAchievementOnsiteSearchDiscoveryRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOnsiteSearchDiscoveryRoas"
                            data-cell-name="ROAS">
                            <div class="achievement-value shopeeAchievementOnsiteSearchDiscoveryRoas"></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="" data-cell-id="shopeeAchievementOnsiteAffiliateFormat" data-cell-name="FORMAT">
                            <div class="format-col">Affiliate</div>
                        </td>
                        <td class="" data-cell-id="shopeeAchievementOnsiteAffiliateBuyingUnit"
                            data-cell-name="BUYING UNIT">
                            <div class="buying-unit-col">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOnsiteAffiliateKpis"
                            data-cell-name="KPIS">
                            <div class="achievement-value shopeeAchievementOnsiteAffiliateKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOnsiteAffiliateSpending"
                            data-cell-name="SPENDING">
                            <div class="achievement-value shopeeAchievementOnsiteAffiliateSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOnsiteAffiliateRevenue"
                            data-cell-name="REVENUE">
                            <div class="achievement-value shopeeAchievementOnsiteAffiliateRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOnsiteAffiliateRoas"
                            data-cell-name="ROAS">
                            <div class="achievement-value shopeeAchievementOnsiteAffiliateRoas"></div>
                        </td>
                    </tr>

                    <tr>
                        <td class="" data-cell-id="shopeeAchievementOnsiteSponsorMediaFormat" data-cell-name="FORMAT">
                            <div class="format-col">Sponsor Media</div>
                        </td>
                        <td class="" data-cell-id="shopeeAchievementOnsiteSponsorMediaBuyingUnit"
                            data-cell-name="BUYING UNIT">
                            <div class="buying-unit-col">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOnsiteSponsorMediaKpis"
                            data-cell-name="KPIS">
                            <div class="achievement-value shopeeAchievementOnsiteSponsorMediaKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOnsiteSponsorMediaSpending"
                            data-cell-name="SPENDING">
                            <div class="achievement-value shopeeAchievementOnsiteSponsorMediaSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOnsiteSponsorMediaRevenue"
                            data-cell-name="REVENUE">
                            <div class="achievement-value shopeeAchievementOnsiteSponsorMediaRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOnsiteSponsorMediaRoas"
                            data-cell-name="ROAS">
                            <div class="achievement-value shopeeAchievementOnsiteSponsorMediaRoas"></div>
                        </td>
                    </tr>

                    <tr>
                        <td rowspan="1">
                            <div class="platform-col">
                                Organic
                            </div>
                        </td>
                        <td class="" data-cell-id="shopeeAchievementOrganicOtherFormat" data-cell-name="FORMAT">
                            <div class="format-col">Other</div>
                        </td>
                        <td class="" data-cell-id="shopeeAchievementOrganicOtherBuyingUnit"
                            data-cell-name="BUYING UNIT">
                            <div class="buying-unit-col">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOrganicOtherKpis"
                            data-cell-name="KPIS">
                            <div class="achievement-value shopeeAchievementOrganicOtherKpis"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOrganicOtherSpending"
                            data-cell-name="SPENDING">
                            <div class="achievement-value shopeeAchievementOrganicOtherSpending"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOrganicOtherRevenue"
                            data-cell-name="REVENUE">
                            <div class="achievement-value shopeeAchievementOrganicOtherRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementOrganicOtherRoas"
                            data-cell-name="ROAS">
                            <div class="achievement-value shopeeAchievementOrganicOtherRoas"></div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

@if (!$view)
@include('reports.ecom.modals.cell-config-modal')
@endif

@endsection
@section('script')

@include('reports.ecom.common.script')

@endsection