@extends('layout.ecom.master')
@section('head')
{{-- import css --}}
<link rel="stylesheet" type="text/css" href="{{url('/template/main')}}/assets/css/select2.min.css" />
{{--
<link href="{{ asset('css/advertiser-performance/custom-calendar.css') }}" rel="stylesheet"> --}}
<link href="{{url('/template/main')}}/assets/css/placeholder-loading.min.css" rel="stylesheet" type="text/css" />
<link rel="stylesheet" type="text/css" href="{{url('/template/main')}}/assets/css/daterangepicker.css" />
{{-- Select chosen CSS--}}
<link rel="stylesheet" type="text/css" href="{{url('/template/main')}}/assets/css/chosen.css" />
<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="{{url('/template/main')}}/assets/css/dataTables.bootstrap5.min.css" />
<link href="{{ asset('css/report/ecom/index.css') }}" rel="stylesheet">

@include('reports.ecom.common.style')

<style>
    /* Select2 Custom Styles for Modal */
    #cellConfigModal .select2-container {
        z-index: 9999 !important;
        width: 100% !important;
    }

    /* Selection box styling */
    #cellConfigModal .select2-container .select2-selection--single {
        height: 38px !important;
        border: 1px solid #ced4da !important;
        border-radius: 0.375rem !important;
        background-color: #fff !important;
    }

    /* Text inside selection */
    #cellConfigModal .select2-container .select2-selection--single .select2-selection__rendered {
        line-height: 36px !important;
        padding-left: 12px !important;
        padding-right: 30px !important;
        color: #495057 !important;
    }

    /* Arrow styling - IMPORTANT */
    #cellConfigModal .select2-container .select2-selection--single .select2-selection__arrow {
        height: 36px !important;
        position: absolute !important;
        top: 1px !important;
        right: 1px !important;
        width: 20px !important;
        display: block !important;
    }

    #cellConfigModal .select2-container .select2-selection--single .select2-selection__arrow b {
        border-color: #999 transparent transparent transparent !important;
        border-style: solid !important;
        border-width: 5px 4px 0 4px !important;
        height: 0 !important;
        left: 50% !important;
        margin-left: -4px !important;
        margin-top: -2px !important;
        position: absolute !important;
        top: 50% !important;
        width: 0 !important;
    }

    /* Dropdown styling with scroll */
    #cellConfigModal .select2-dropdown {
        border: 1px solid #ced4da !important;
        border-radius: 0.375rem !important;
        z-index: 9999 !important;
        background-color: #fff !important;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
    }

    /* Results container with scroll */
    #cellConfigModal .select2-results {
        max-height: 200px !important;
        overflow-y: auto !important;
    }

    /* Individual result items */
    #cellConfigModal .select2-results__option {
        padding: 8px 12px !important;
        color: #495057 !important;
        cursor: pointer !important;
    }

    #cellConfigModal .select2-results__option:hover,
    #cellConfigModal .select2-results__option--highlighted {
        background-color: #e9ecef !important;
        color: #495057 !important;
    }

    #cellConfigModal .select2-results__option--selected {
        background-color: #0d6efd !important;
        color: #fff !important;
    }

    /* Search box styling */
    #cellConfigModal .select2-search--dropdown {
        padding: 4px !important;
        border-bottom: 1px solid #ced4da !important;
    }

    #cellConfigModal .select2-search--dropdown .select2-search__field {
        border: 1px solid #ced4da !important;
        border-radius: 0.25rem !important;
        padding: 4px 8px !important;
        width: 100% !important;
        box-sizing: border-box !important;
    }

    /* Ensure Select2 dropdown appears above modal */
    .select2-container--open .select2-dropdown {
        z-index: 9999 !important;
    }

    /* Fix for opened state arrow */
    #cellConfigModal .select2-container--open .select2-selection--single .select2-selection__arrow b {
        border-color: transparent transparent #999 transparent !important;
        border-width: 0 4px 5px 4px !important;
    }
</style>
@endsection
@section('main-content')

<div class="report-container">
    <div class="report-header">
        <div class="reportrange-container">
            <div class="overview-reportrange">
                <div id="reportrange" style="">
                    <i class="fa fa-calendar"></i>&nbsp;
                    <span></span> <i class="fa fa-caret-down"></i>
                </div>
            </div>
        </div>

        <div class="title">{{ $title ?? '' }}</div>
        <div class="logo"><img src="/imgs/clients/cuckoo.jpeg" alt=""></div>

        <!-- Auto-detect col_index button -->
        {{-- <div class="auto-detect-container" style="position: absolute; top: 10px; right: 120px;">
            <button type="button" class="btn btn-outline-primary btn-sm" id="autoDetectColIndexBtn"
                title="Auto-detect column indexes for configurations">
                <i class="fa fa-magic"></i> Auto-detect Columns
            </button>
        </div> --}}
    </div>

    <div class="report-content">
        <div class="table-container">
            <table class="table" id="overall" data-name="overall" data-id="overall">
                <tbody>
                    <tr>
                        <td colspan="9">
                            <div class="group">PLAN</div>
                        </td>
                        <td colspan="7">
                            <div class="group">ACTUAL
                            </div>
                        </td>
                        <td colspan="2">
                            <div class="group">ACHIEVEMENT
                            </div>
                        </td>
                    </tr>

                    <tr class="header-row">
                        <td>
                            <div>CHANNEL</div>
                        </td>
                        <td>
                            <div>TIMELINE</div>
                        </td>
                        <td>
                            <div>Spending</div>
                        </td>
                        <td>
                            <div>Revenue <br>(Excl. Cancellation)</div>
                        </td>
                        <td>
                            <div>CPP</div>
                        </td>
                        <td>
                            <div>Traffic</div>
                        </td>
                        <td>
                            <div>AOV</div>
                        </td>
                        <td>
                            <div>ROAS</div>
                        </td>
                        <td>
                            <div>Cancellation Rate</div>
                        </td>
                        <td>
                            <div>Spending</div>
                        </td>
                        <td>
                            <div>Revenue <br>(Excl. Cancellation)</div>
                        </td>
                        <td>
                            <div>CPP</div>
                        </td>
                        <td>
                            <div>Traffic</div>
                        </td>
                        <td>
                            <div>AOV</div>
                        </td>
                        <td>
                            <div>ROAS</div>
                        </td>
                        <td>
                            <div>Cancellation Rate</div>
                        </td>
                        <td>
                            <div>%Spending</div>
                        </td>
                        <td>
                            <div>%Revenue <br>(Excl. Cancellation)</div>
                        </td>
                    </tr>

                    <tr>
                        <td class="channel-col">
                            <div>LAZADA</div>
                        </td>
                        <td rowspan="4" style="margin: 10px">
                            <div class="timelinePercentage tb-timeline overall-percentage">-%</div>
                        </td>

                        <td class="configurable-cell" data-cell-id="lazadaPlanSpending">
                            <div class="plan-value lazadaPlanSpending">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="lazadaPlanRevenueExCancel">
                            <div class="plan-value lazadaPlanRevenueExCancel">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="lazadaPlanCpp">
                            <div class="plan-value lazadaPlanCpp">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="lazadaPlanTraffic">
                            <div class="plan-value lazadaPlanTraffic">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="lazadaPlanAov">
                            <div class="plan-value lazadaPlanAov">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="lazadaPlanRoas">
                            <div class="plan-value lazadaPlanRoas">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="lazadaPlanCancelRate">
                            <div class="plan-value lazadaPlanCancelRate">-</div>
                        </td>

                        <td class="configurable-cell" data-cell-id="lazadaActualSpending">
                            <div class="actual-value lazadaActualSpending">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="lazadaActualRevenueExCancel">
                            <div class="actual-value lazadaActualRevenueExCancel">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="lazadaActualCpp">
                            <div class="actual-value lazadaActualCpp">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="lazadaActualTraffic">
                            <div class="actual-value lazadaActualTraffic">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="lazadaActualAov">
                            <div class="actual-value lazadaActualAov">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="lazadaActualRoas">
                            <div class="actual-value lazadaActualRoas">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="lazadaActualCancelRate">
                            <div class="actual-value lazadaActualCancelRate">-</div>
                        </td>

                        <td class="configurable-cell" data-cell-id="lazadaAchievementSpending">
                            <div class="achievement-value lazadaAchievementSpending">-%</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="lazadaAchievementRevenueEx">
                            <div class="achievement-value lazadaAchievementRevenueEx">-%</div>
                        </td>
                    </tr>
                    <tr>
                        <td class="channel-col">
                            <div>SHOPEE</div>
                        </td>

                        <td class="configurable-cell" data-cell-id="shopeePlanSpending" data-cell-name="PLAN SPENDING">
                            <div class="plan-value shopeePlanSpending">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeePlanRevenueExCancel"
                            data-cell-name="PLAN REVENUE">
                            <div class="plan-value shopeePlanRevenueExCancel">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeePlanCpp" data-cell-name="PLAN CPP">
                            <div class="plan-value shopeePlanCpp">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeePlanTraffic" data-cell-name="PLAN TRAFFIC">
                            <div class="plan-value shopeePlanTraffic">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeePlanAov" data-cell-name="PLAN AOV">
                            <div class="plan-value shopeePlanAov">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeePlanRoas" data-cell-name="PLAN ROAS">
                            <div class="plan-value shopeePlanRoas">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeePlanCancelRate"
                            data-cell-name="PLAN CANCELLATION RATE">
                            <div class="plan-value shopeePlanCancelRate">-</div>
                        </td>

                        <td class="configurable-cell" data-cell-id="shopeeActualSpending"
                            data-cell-name="ACTUAL SPENDING">
                            <div class="actual-value shopeeActualSpending">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualRevenueExCancel"
                            data-cell-name="ACTUAL REVENUE">
                            <div class="actual-value shopeeActualRevenueExCancel">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualCpp" data-cell-name="ACTUAL CPP">
                            <div class="actual-value shopeeActualCpp">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualTraffic"
                            data-cell-name="ACTUAL TRAFFIC">
                            <div class="actual-value shopeeActualTraffic">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualAov" data-cell-name="ACTUAL AOV">
                            <div class="actual-value shopeeActualAov">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualRoas" data-cell-name="ACTUAL ROAS">
                            <div class="actual-value shopeeActualRoas">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeActualCancelRate"
                            data-cell-name="ACTUAL CANCELLATION RATE">
                            <div class="actual-value shopeeActualCancelRate">-</div>
                        </td>

                        <td class="configurable-cell" data-cell-id="shopeeAchievementSpending"
                            data-cell-name="ACHIEVEMENT SPENDING">
                            <div class="achievement-value shopeeAchievementSpending">-%</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="shopeeAchievementRevenueEx"
                            data-cell-name="ACHIEVEMENT REVENUE">
                            <div class="achievement-value shopeeAchievementRevenueEx">-%</div>
                        </td>
                    </tr>
                    <tr>
                        <td class="channel-col">
                            <div>TIKI</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tikiPlanSpending" data-cell-name="PLAN SPENDING">
                            <div class="plan-value tikiPlanSpending">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tikiPlanRevenueExCancel"
                            data-cell-name="PLAN REVENUE">
                            <div class="plan-value tikiPlanRevenueExCancel">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tikiPlanCpp" data-cell-name="PLAN CPP">
                            <div class="plan-value tikiPlanCpp">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tikiPlanTraffic" data-cell-name="PLAN TRAFFIC">
                            <div class="plan-value tikiPlanTraffic">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tikiPlanAov" data-cell-name="PLAN AOV">
                            <div class="plan-value tikiPlanAov">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tikiPlanRoas" data-cell-name="PLAN ROAS">
                            <div class="plan-value tikiPlanRoas">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tikiPlanCancelRate"
                            data-cell-name="PLAN CANCELLATION RATE">
                            <div class="plan-value tikiPlanCancelRate">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tikiActualSpending"
                            data-cell-name="ACTUAL SPENDING">
                            <div class="actual-value tikiActualSpending">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tikiActualRevenueExCancel"
                            data-cell-name="ACTUAL REVENUE">
                            <div class="actual-value tikiActualRevenueExCancel">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tikiActualCpp" data-cell-name="ACTUAL CPP">
                            <div class="actual-value tikiActualCpp">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tikiActualTraffic" data-cell-name="ACTUAL TRAFFIC">
                            <div class="actual-value tikiActualTraffic">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tikiActualAov" data-cell-name="ACTUAL AOV">
                            <div class="actual-value tikiActualAov">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tikiActualRoas" data-cell-name="ACTUAL ROAS">
                            <div class="actual-value tikiActualRoas">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tikiActualCancelRate"
                            data-cell-name="ACTUAL CANCELLATION RATE">
                            <div class="actual-value tikiActualCancelRate">-</div>
                        </td>

                        <td class="configurable-cell" data-cell-id="tikiAchievementSpending"
                            data-cell-name="ACHIEVEMENT SPENDING">
                            <div class="achievement-value tikiAchievementSpending">-%</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tikiAchievementRevenueEx"
                            data-cell-name="ACHIEVEMENT REVENUE">
                            <div class="achievement-value tikiAchievementRevenueEx">-%</div>
                        </td>
                    </tr>
                    <tr class="total-row">
                        <td class="channel-col">
                            <div>TOTAL</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="totalPlanSpending" data-cell-name="PLAN SPENDING">
                            <div class="plan-value totalPlanSpending">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="totalPlanRevenueExCancel"
                            data-cell-name="PLAN REVENUE">
                            <div class="plan-value totalPlanRevenueExCancel">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="totalPlanCpp" data-cell-name="PLAN CPP">
                            <div class="plan-value totalPlanCpp">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="totalPlanTraffic" data-cell-name="PLAN TRAFFIC">
                            <div class="plan-value totalPlanTraffic">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="totalPlanAov" data-cell-name="PLAN AOV">
                            <div class="plan-value totalPlanAov">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="totalPlanRoas" data-cell-name="PLAN ROAS">
                            <div class="plan-value totalPlanRoas">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="totalPlanCancelRate"
                            data-cell-name="PLAN CANCELLATION RATE">
                            <div class="plan-value totalPlanCancelRate">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="totalActualSpending"
                            data-cell-name="ACTUAL SPENDING">
                            <div class="actual-value totalActualSpending">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="totalActualRevenueExCancel"
                            data-cell-name="ACTUAL REVENUE">
                            <div class="actual-value totalActualRevenueExCancel">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="totalActualCpp" data-cell-name="ACTUAL CPP">
                            <div class="actual-value totalActualCpp">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="totalActualTraffic" data-cell-name="ACTUAL TRAFFIC">
                            <div class="actual-value totalActualTraffic">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="totalActualAov" data-cell-name="ACTUAL AOV">
                            <div class="actual-value totalActualAov">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="totalActualRoas" data-cell-name="ACTUAL ROAS">
                            <div class="actual-value totalActualRoas">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="totalActualCancelRate"
                            data-cell-name="ACTUAL CANCELLATION RATE">
                            <div class="actual-value totalActualCancelRate">-</div>
                        </td>

                        <td class="configurable-cell" data-cell-id="totalAchievementSpending"
                            data-cell-name="ACHIEVEMENT SPENDING">
                            <div class="achievement-value totalAchievementSpending">-%</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="totalAchievementRevenueEx"
                            data-cell-name="ACHIEVEMENT REVENUE">
                            <div class="achievement-value totalAchievementRevenueEx">-%</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>


        <div class="revenue-table-container">
            <table class="table" id="actual-brx-ckv" data-name="overall" data-id="overall">
                <tbody>
                    <tr>
                        <td></td>
                        <td colspan="2">
                            <div class="group">REV​ENUE - ACTUAL BRX & CKV
                            </div>
                        </td>
                    </tr>

                    <tr class="header-row">
                        <td>
                        </td>
                        <td>
                            <div>REVENUE - DASHBOARD BRX
                            </div>
                        </td>
                        <td>
                            <div>REVENUE - ACTUAL CKV
                            </div>
                        </td>
                    </tr>
                    <tr class="header-row">
                        <td>
                        </td>
                        <td>
                            <div>Revenue
                                (Exclu. Cancelation)
                            </div>
                        </td>
                        <td>
                            <div>Actual Revenue
                            </div>
                        </td>
                    </tr>

                    <tr>
                        <td class="channel-col">
                            <div>LAZADA</div>
                        </td>

                        <td class="configurable-cell" data-cell-id="ckvLazadaActualRevenueExCancel">
                            <div class="actual-value ckvLazadaActualRevenueExCancel">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="ckvLazadaActualRevenueCkv">
                            <div class="actual-value ckvLazadaActualRevenueCkv">-</div>
                        </td>

                    </tr>
                    <tr>
                        <td class="channel-col">
                            <div>SHOPEE</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="ckvShopeeActualRevenueExCancel">
                            <div class="actual-value ckvShopeeActualRevenueExCancel">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="ckvShopeeActualRevenueCkv">
                            <div class="actual-value ckvShopeeActualRevenueCkv">-</div>
                        </td>
                    </tr>
                    <tr>
                        <td class="channel-col">
                            <div>TIKI</div>
                        </td>

                        <td class="configurable-cell" data-cell-id="ckvTikiActualRevenueExCancel">
                            <div class="actual-value ckvTikiActualRevenueExCancel">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="ckvTikiActualRevenueCkv">
                            <div class="actual-value ckvTikiActualRevenueCkv">-</div>
                        </td>

                    </tr>
                    <tr class="total-row">
                        <td class="channel-col">
                            <div>TOTAL</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="ckvTotalActualRevenueExCancel">
                            <div class="actual-value ckvTotalActualRevenueExCancel">-</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="ckvTotalActualRevenueCkv">
                            <div class="actual-value ckvTotalActualRevenueCkv">-</div>
                        </td>

                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="report-comment">
        <div class="weekly-comment-header"><strong>Weekly comment</strong></div>
        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th>Week</th>
                    <th>Comment</th>
                </tr>
            </thead>
            <tbody>
            </tbody>
        </table>
    </div>
</div>

<!-- Cell Configuration Modal -->
@if (!$view)
@include('reports.ecom.modals.cell-config-modal')
@endif

@endsection
@section('script')

@include('reports.ecom.common.script')

@endsection