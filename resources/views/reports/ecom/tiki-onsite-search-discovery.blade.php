@extends('layout.ecom.master')
@section('head')
{{-- import css --}}
<link rel="stylesheet" type="text/css" href="{{url('/template/main')}}/assets/css/select2.min.css" />
<link href="{{url('/template/main')}}/assets/css/placeholder-loading.min.css" rel="stylesheet" type="text/css" />
<link rel="stylesheet" type="text/css" href="{{url('/template/main')}}/assets/css/daterangepicker.css" />
<link rel="stylesheet" type="text/css" href="{{url('/template/main')}}/assets/css/chosen.css" />
<link rel="stylesheet" type="text/css" href="{{url('/template/main')}}/assets/css/dataTables.bootstrap5.min.css" />
<link href="{{ asset('css/report/ecom/index.css') }}" rel="stylesheet">
@include('reports.ecom.common.style')
<style>
    .page-content {
        padding: 0;
    }

    .report-content {
        margin-bottom: 4rem;
    }

    .metric-card {
        background: #fff;
        border: 1px solid #ddd;
        padding: 15px;
        margin: 10px 0;
        text-align: center;
    }

    .percentage-card {
        background: #fff;
        border: 1px solid #ddd;
        border-left: none;
        padding: 15px;
        margin: 10px 0;
        text-align: center;
        color: #fff;
        background-color: #7A211C;
    }

    .percentage-card .metric-label {
        color: #fff;
    }

    .percentage-card .metric-value {
        color: #fff;
    }

    .metric-value {
        font-size: 24px;
        font-weight: bold;
        color: #333;
    }

    .metric-label {
        font-size: 12px;
        color: #666;
        margin-top: 5px;
    }

    .chart-container {
        background: #fff;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
    }

    .apexcharts-legend {
        left: 0 !important;
        right: auto !important;
    }
</style>
@endsection

@section('main-content')

<div class="report-container">
    <div class="report-header">
        <div class="reportrange-container">
            <div class="overview-reportrange">
                <div id="reportrange" style="">
                    <i class="fa fa-calendar"></i>&nbsp;
                    <span></span> <i class="fa fa-caret-down"></i>
                </div>
            </div>
        </div>

        <div class="title">CCUCKOO - TIKI - ONSITE - SEARCH / DISCOVERY</div>
        <div class="logo"><img src="/imgs/clients/cuckoo.jpeg" alt="CUCKOO"></div>
    </div>

    <div class="report-content">
        <div class="timeline ms-4 mt-2 p-0 d-flex gap-2 justify-content-start">
            <div class="timeline-label" style="width: 100px;">%TIMELINE
            </div>
            <div class="timeline-value overall-percentage" style="width: 100px;">-%</div>
        </div>

        <!-- PLAN Section -->
        <div class="group group-section mt-4 ms-4">PLAN</div>

        <div class="table-container">
            <table class="table" id="tiki-offsite-fb-conversion" data-name="tiki-offsite-fb-conversion"
                data-id="tiki-offsite-fb-conversion">
                <tbody>
                    <tr class="header-row">
                        <td>
                            <div>PLATFORM</div>
                        </td>
                        <td>
                            <div>FORMAT</div>
                        </td>
                        <td>
                            <div>BUYING<br />METHOD</div>
                        </td>
                        <td>
                            <div>PURCHASE</div>
                        </td>
                        <td>
                            <div>EST.<br />% CR</div>
                        </td>
                        <td>
                            <div>EST.<br />ATC</div>
                        </td>
                        <td>
                            <div>EST.<br />%ATC RATE</div>
                        </td>
                        <td>
                            <div>EST.<br />CPP</div>
                        </td>
                        <td>
                            <div>EST.<br />AOV</div>
                        </td>
                        <td>
                            <div>EST.<br />ROAS</div>
                        </td>
                        <td>
                            <div>EST.<br />REVENUE</div>
                        </td>
                        <td>
                            <div>EST.<br />NET BUDGET</div>
                        </td>
                    </tr>

                    <tr>
                        <td class="" data-cell-id="planPlatform">
                            <div class="plan-value">ON - SITE</div>
                        </td>
                        <td class="" data-cell-id="planFormat">
                            <div class="plan-value">Search <br> Discovery</div>
                        </td>
                        <td class="" data-cell-id="planBuyingMethod">
                            <div class="plan-value">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="planPurchase">
                            <div class="plan-value planPurchase"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="planCr">
                            <div class="plan-value planCr"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="planAtc">
                            <div class="plan-value planAtc"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="planPercentageAtcRate">
                            <div class="plan-value planPercentageAtcRate"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="planCpp">
                            <div class="plan-value planCpp"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="planAov">
                            <div class="plan-value planAov"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="planRoas">
                            <div class="plan-value planRoas"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="planRevenue">
                            <div class="plan-value planRevenue"></div>
                        </td>
                        <td class="configurable-cell" data-cell-id="planNetBudget">
                            <div class="plan-value planNetBudget"></div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- ACTUAL Section -->
        <div class="group group-section mt-4 ms-4">ACTUAL</div>

        <!-- Metrics Cards Row -->
        <div class="row mt-4 ms-5 justify-content-center">
            <div class="col-md-3 d-flex">
                <div class="metric-card" style="width: 60%;">
                    <div class="metric-label">Purchase (Exclu. Cancellation)</div>
                    <div class="metric-value configurable-cell actualPurchaseExcludeCancellation"
                        data-cell-id="actualPurchaseExcludeCancellation">-</div>
                </div>
                <div class="percentage-card" style="width: 40%;">
                    <div class="metric-label">% Plan</div>
                    <div class="metric-value configurable-cell actualPurchaseExcludeCancellationPlanPercentage"
                        data-cell-id="actualPurchaseExcludeCancellationPlanPercentage">-%</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="metric-card">
                    <div class="metric-label">CPP</div>
                    <div class="metric-value configurable-cell actualCpp" data-cell-id="actualCpp">-</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="metric-card">
                    <div class="metric-label">AOV</div>
                    <div class="metric-value configurable-cell aov" data-cell-id="aov">-%
                    </div>
                </div>
            </div>
            <div class="col-md-3 d-flex">
                <div class="metric-card" style="width: 60%;">
                    <div class="metric-label">ATC</div>
                    <div class="metric-value configurable-cell actualAtc" data-cell-id="actualAtc">-</div>
                </div>
                <div class="percentage-card" style="width: 40%;">
                    <div class="metric-label">% Plan</div>
                    <div class="metric-value configurable-cell actualAtcPlanPercentage"
                        data-cell-id="actualAtcPlanPercentage">-%</div>
                </div>
            </div>
        </div>

        <div class="row mt-4 ms-5 justify-content-center">
            <div class="col-md-3 d-flex">
                <div class="metric-card" style="width: 60%;">
                    <div class="metric-label">Revenue (Exclu. Cancellation)</div>
                    <div class="metric-value configurable-cell actualRevenueExcluCancellation"
                        data-cell-id="actualRevenueExcluCancellation">-</div>
                </div>
                <div class="percentage-card" style="width: 40%;">
                    <div class="metric-label">% Plan</div>
                    <div class="metric-value configurable-cell actualRevenueExcluCancellationPlanPercentage"
                        data-cell-id="actualRevenueExcluCancellationPlanPercentage">-%</div>
                </div>
            </div>
            <div class="col-md-3 d-flex">
                <div class="metric-card" style="width: 60%;">
                    <div class="metric-label">Spending</div>
                    <div class="metric-value configurable-cell actualSpending" data-cell-id="actualSpending">-
                    </div>
                </div>
                <div class="percentage-card" style="width: 40%;">
                    <div class="metric-label">% Plan</div>
                    <div class="metric-value configurable-cell actualNetSpendingPlanPercentage"
                        data-cell-id="actualNetSpendingPlanPercentage">-%</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="metric-card">
                    <div class="metric-label">ROAS</div>
                    <div class="metric-value configurable-cell actualRoas" data-cell-id="actualRoas">-</div>
                </div>
            </div>
        </div>


        <!-- Daily Performance Chart -->
        <div class="chart-container ms-4">
            <h5 class="header-group">
                DAILY
                PERFORMANCE</h5>
            <div id="dailyPerformanceChart" style="height: 300px;"></div>
        </div>

        <!-- Performance Table -->
        <div class="table-container mt-4">
            <table class="table table-striped" id="performance-table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Impression</th>
                        <th>Click</th>
                        <th>Outbound Click</th>
                        <th>Traffic</th>
                        <th>ATC</th>
                        <th>ATC Value</th>
                        <th>Purchase (Excl.)</th>
                        <th>Purchase (Excl.)</th>
                        <th>%CTR</th>
                        <th>%OCR</th>
                        <th>Visit Rate</th>
                        <th>%ATC</th>
                        <th>%Purchase</th>
                        <th>AOV</th>
                        <th>Revenue (Excl.)</th>
                        <th>ROAS (Excl.)</th>
                        <th>Cancellation</th>
                        <th>Revenue (Incl.)</th>
                        <th>ROAS (Excl.)</th>
                        <th>Unit Cost</th>
                        <th>Spending</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Additional rows would be populated dynamically -->
                </tbody>
            </table>
        </div>
    </div>
</div>

@if (!$view)
@include('reports.ecom.modals.cell-config-modal')
@endif

@endsection
@section('script')

@include('reports.ecom.common.script')

@endsection