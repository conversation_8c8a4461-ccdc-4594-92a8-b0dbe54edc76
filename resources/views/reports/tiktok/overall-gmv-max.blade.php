@extends('layout.ecom.master')
@section('head')
{{-- import css --}}
<link rel="stylesheet" type="text/css" href="{{url('/template/main')}}/assets/css/select2.min.css" />
<link href="{{url('/template/main')}}/assets/css/placeholder-loading.min.css" rel="stylesheet" type="text/css" />
<link rel="stylesheet" type="text/css" href="{{url('/template/main')}}/assets/css/daterangepicker.css" />
<link rel="stylesheet" type="text/css" href="{{url('/template/main')}}/assets/css/chosen.css" />
<link rel="stylesheet" type="text/css" href="{{url('/template/main')}}/assets/css/dataTables.bootstrap5.min.css" />
<link href="{{ asset('css/report/ecom/index.css') }}" rel="stylesheet">

@include('reports.ecom.common.style')
@endsection

@section('main-content')

<div class="report-container">
    <div class="report-header">
        <div class="reportrange-container">
            <div class="overview-reportrange">
                <div id="reportrange" style="">
                    <i class="fa fa-calendar"></i>&nbsp;
                    <span></span> <i class="fa fa-caret-down"></i>
                </div>
            </div>
        </div>

        <div class="title">CUCKOO - OVERALL - GMV MAX</div>
        <div class="logo"><img src="/imgs/clients/cuckoo.jpeg" alt=""></div>
    </div>

    <div class="report-content">
        <!-- PLAN Section -->
        <div class="table-container">
            <table class="table" id="plan-table" data-name="plan" data-id="plan">
                <tbody>
                    <tr>
                        <td colspan="9">
                            <div class="group">PLAN</div>
                        </td>
                    </tr>

                    <tr class="header-row">
                        <td>
                            <div>CHANNEL</div>
                        </td>
                        <td>
                            <div>PLATFORM</div>
                        </td>
                        <td>
                            <div>TIMELINE</div>
                        </td>
                        <td>
                            <div>FORMAT</div>
                        </td>
                        <td>
                            <div>BUYING<br />UNIT</div>
                        </td>
                        <td>
                            <div>KPIs</div>
                        </td>
                        <td>
                            <div>SPENDING</div>
                        </td>
                        <td>
                            <div>REVENUE<br />(Incl. Cancellation)</div>
                        </td>
                        <td>
                            <div>ROAS</div>
                        </td>
                    </tr>

                    <!-- TIKTOK Rows -->
                    <tr>
                        <td class="channel-col" rowspan="2">
                            <div>TIKTOK</div>
                        </td>
                        <td rowspan="2">
                            <div class="platform-col">
                                On-site
                            </div>
                        </td>
                        <td rowspan="2">
                            <div class="timeline-col">
                                2025
                            </div>
                        </td>
                        <td class="" data-cell-id="tiktokPlanGmvMaxVideoFormat" data-cell-name="FORMAT">
                            <div class="plan-value format-col">TikTok - GMV Max Video</div>
                        </td>
                        <td class="" data-cell-id="tiktokPlanGmvMaxVideoBuyingUnit" data-cell-name="BUYING UNIT">
                            <div class="plan-value buying-unit-col">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokPlanGmvMaxVideoKpis" data-cell-name="KPIS">
                            <div class="plan-value tiktokPlanGmvMaxVideoKpis">97</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokPlanGmvMaxVideoSpending"
                            data-cell-name="SPENDING">
                            <div class="plan-value tiktokPlanGmvMaxVideoSpending">29.03M</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokPlanGmvMaxVideoRevenue"
                            data-cell-name="REVENUE">
                            <div class="plan-value tiktokPlanGmvMaxVideoRevenue">116.13M</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokPlanGmvMaxVideoRoas" data-cell-name="ROAS">
                            <div class="plan-value tiktokPlanGmvMaxVideoRoas">4.0</div>
                        </td>
                    </tr>

                    <tr>
                        <td class="" data-cell-id="tiktokPlanGmvMaxLivestreamFormat" data-cell-name="FORMAT">
                            <div class="plan-value format-col">TikTok - GMV Max Livestream</div>
                        </td>
                        <td class="" data-cell-id="tiktokPlanGmvMaxLivestreamBuyingUnit" data-cell-name="BUYING UNIT">
                            <div class="plan-value buying-unit-col">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokPlanGmvMaxLivestreamKpis"
                            data-cell-name="KPIS">
                            <div class="plan-value tiktokPlanGmvMaxLivestreamKpis">112</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokPlanGmvMaxLivestreamSpending"
                            data-cell-name="SPENDING">
                            <div class="plan-value tiktokPlanGmvMaxLivestreamSpending">33.50M</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokPlanGmvMaxLivestreamRevenue"
                            data-cell-name="REVENUE">
                            <div class="plan-value tiktokPlanGmvMaxLivestreamRevenue">133.99M</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokPlanGmvMaxLivestreamRoas"
                            data-cell-name="ROAS">
                            <div class="plan-value tiktokPlanGmvMaxLivestreamRoas">6.7</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- ACTUAL Section -->
        <div class="table-container">
            <table class="table" id="actual-table" data-name="actual" data-id="actual">
                <tbody>
                    <tr>
                        <td colspan="9">
                            <div class="group">ACTUAL</div>
                        </td>
                    </tr>

                    <tr class="header-row">
                        <td>
                            <div>CHANNEL</div>
                        </td>
                        <td>
                            <div>PLATFORM</div>
                        </td>
                        <td>
                            <div>TIMELINE</div>
                        </td>
                        <td>
                            <div>FORMAT</div>
                        </td>
                        <td>
                            <div>BUYING<br />UNIT</div>
                        </td>
                        <td>
                            <div>KPIs</div>
                        </td>
                        <td>
                            <div>SPENDING</div>
                        </td>
                        <td>
                            <div>REVENUE<br />(Incl. Cancellation)</div>
                        </td>
                        <td>
                            <div>ROAS</div>
                        </td>
                    </tr>

                    <!-- TIKTOK Actual Rows -->
                    <tr>
                        <td class="channel-col" rowspan="2">
                            <div>TIKTOK</div>
                        </td>
                        <td rowspan="2">
                            <div class="platform-col">
                                On-site
                            </div>
                        </td>
                        <td rowspan="2">
                            <div class="timeline-col">
                                2025
                            </div>
                        </td>
                        <td class="" data-cell-id="tiktokActualGmvMaxVideoFormat" data-cell-name="FORMAT">
                            <div class="actual-value format-col">TikTok - GMV Max Video</div>
                        </td>
                        <td class="" data-cell-id="tiktokActualGmvMaxVideoBuyingUnit" data-cell-name="BUYING UNIT">
                            <div class="actual-value buying-unit-col">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokActualGmvMaxVideoKpis" data-cell-name="KPIS">
                            <div class="actual-value tiktokActualGmvMaxVideoKpis">10</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokActualGmvMaxVideoSpending"
                            data-cell-name="SPENDING">
                            <div class="actual-value tiktokActualGmvMaxVideoSpending">11.78M</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokActualGmvMaxVideoRevenue"
                            data-cell-name="REVENUE">
                            <div class="actual-value tiktokActualGmvMaxVideoRevenue">19.56M</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokActualGmvMaxVideoRoas" data-cell-name="ROAS">
                            <div class="actual-value tiktokActualGmvMaxVideoRoas">1.7</div>
                        </td>
                    </tr>

                    <tr>
                        <td class="" data-cell-id="tiktokActualGmvMaxLivestreamFormat" data-cell-name="FORMAT">
                            <div class="actual-value format-col">TikTok - GMV Max Livestream</div>
                        </td>
                        <td class="" data-cell-id="tiktokActualGmvMaxLivestreamBuyingUnit" data-cell-name="BUYING UNIT">
                            <div class="actual-value buying-unit-col">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokActualGmvMaxLivestreamKpis"
                            data-cell-name="KPIS">
                            <div class="actual-value tiktokActualGmvMaxLivestreamKpis">5</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokActualGmvMaxLivestreamSpending"
                            data-cell-name="SPENDING">
                            <div class="actual-value tiktokActualGmvMaxLivestreamSpending">10.41M</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokActualGmvMaxLivestreamRevenue"
                            data-cell-name="REVENUE">
                            <div class="actual-value tiktokActualGmvMaxLivestreamRevenue">8.19M</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokActualGmvMaxLivestreamRoas"
                            data-cell-name="ROAS">
                            <div class="actual-value tiktokActualGmvMaxLivestreamRoas">0.8</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- %ACHIEVEMENT Section -->
        <div class="table-container">
            <table class="table" id="achievement-table" data-name="achievement" data-id="achievement">
                <tbody>
                    <tr>
                        <td colspan="9">
                            <div class="group">%ACHIEVEMENT</div>
                        </td>
                    </tr>

                    <tr class="header-row">
                        <td>
                            <div>CHANNEL</div>
                        </td>
                        <td>
                            <div>PLATFORM</div>
                        </td>
                        <td>
                            <div>TIMELINE</div>
                        </td>
                        <td>
                            <div>FORMAT</div>
                        </td>
                        <td>
                            <div>BUYING<br />UNIT</div>
                        </td>
                        <td>
                            <div>KPIs</div>
                        </td>
                        <td>
                            <div>SPENDING</div>
                        </td>
                        <td>
                            <div>REVENUE<br />(Incl. Cancellation)</div>
                        </td>
                        <td>
                            <div>ROAS</div>
                        </td>
                    </tr>

                    <!-- TIKTOK Achievement Rows -->
                    <tr>
                        <td class="channel-col" rowspan="2">
                            <div>TIKTOK</div>
                        </td>
                        <td rowspan="2">
                            <div class="platform-col">
                                On-site
                            </div>
                        </td>
                        <td rowspan="2">
                            <div class="timeline-col">
                                2025
                            </div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokAchievementGmvMaxVideoFormat"
                            data-cell-name="FORMAT">
                            <div class="achievement-value format-col">TikTok - GMV Max Video</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokAchievementGmvMaxVideoBuyingUnit"
                            data-cell-name="BUYING UNIT">
                            <div class="achievement-value buying-unit-col">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokAchievementGmvMaxVideoKpis"
                            data-cell-name="KPIS">
                            <div class="achievement-value tiktokAchievementGmvMaxVideoKpis">10%</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokAchievementGmvMaxVideoSpending"
                            data-cell-name="SPENDING">
                            <div class="achievement-value tiktokAchievementGmvMaxVideoSpending">40.59%</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokAchievementGmvMaxVideoRevenue"
                            data-cell-name="REVENUE">
                            <div class="achievement-value tiktokAchievementGmvMaxVideoRevenue">16.85%</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokAchievementGmvMaxVideoRoas"
                            data-cell-name="ROAS">
                            <div class="achievement-value tiktokAchievementGmvMaxVideoRoas">1.7</div>
                        </td>
                    </tr>

                    <tr>
                        <td class="configurable-cell" data-cell-id="tiktokAchievementGmvMaxLivestreamFormat"
                            data-cell-name="FORMAT">
                            <div class="achievement-value format-col">TikTok - GMV Max Livestream</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokAchievementGmvMaxLivestreamBuyingUnit"
                            data-cell-name="BUYING UNIT">
                            <div class="achievement-value buying-unit-col">Purchase</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokAchievementGmvMaxLivestreamKpis"
                            data-cell-name="KPIS">
                            <div class="achievement-value tiktokAchievementGmvMaxLivestreamKpis">4%</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokAchievementGmvMaxLivestreamSpending"
                            data-cell-name="SPENDING">
                            <div class="achievement-value tiktokAchievementGmvMaxLivestreamSpending">31.07%</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokAchievementGmvMaxLivestreamRevenue"
                            data-cell-name="REVENUE">
                            <div class="achievement-value tiktokAchievementGmvMaxLivestreamRevenue">16.85%</div>
                        </td>
                        <td class="configurable-cell" data-cell-id="tiktokAchievementGmvMaxLivestreamRoas"
                            data-cell-name="ROAS">
                            <div class="achievement-value tiktokAchievementGmvMaxLivestreamRoas">0.8</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="report-comment">
        <div class="weekly-comment-header"><strong>Weekly comment</strong></div>
        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th>Week</th>
                    <th>Comment</th>
                </tr>
            </thead>
            <tbody>
            </tbody>
        </table>
    </div>
</div>

@include('reports.ecom.modals.cell-config-modal')

@endsection
@section('script')

@include('reports.ecom.common.script')

<script>
    const sheetId = "{{ $report->sheet_id ?? '' }}";
    const reportId = "{{ $report->id ?? '' }}";
    // Make reportId available globally for the JavaScript modules
    window.reportId = reportId;
    window.sheetId = sheetId;
    window.apiGetWeeklyCommentsUrl = "{{ route('ecom-report.weekly-comments', $report->id) }}";
</script>

<script src="{{ url('js/pages/ecom-report.js') }}"></script>

@endsection