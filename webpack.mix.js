const mix = require("laravel-mix");
const resourcesFolder = "resources/js";
const outPutFolder = "public/js";
let fs = require("fs");
let getFiles = function (dir, isUseResourceFolder = true) {
    const dirPath = isUseResourceFolder ? `${resourcesFolder}/${dir}` : dir;
    return fs.readdirSync(dirPath).filter((file) => {
        return fs.statSync(`${dirPath}/${file}`).isFile();
    });
};

function generate(fileName, folder) {
    mix.js(
        `${resourcesFolder}/${folder}/${fileName}`,
        `${outPutFolder}/${folder}`
    );
}

getFiles("pages").forEach((fileName) => {
    generate(fileName, "pages");
});

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel applications. By default, we are compiling the CSS
 | file for the application as well as bundling up all the JS files.
 |
 */

mix.js("resources/js/app.js", "public/js")
    .postCss("resources/css/app.css", "public/css", [
        //
    ])
    .js("resources/js/creative/index.js", "public/js/creative_index.js")
    .js("resources/js/creative/create.js", "public/js/creative_create.js")
    .postCss(
        "resources/css/creative/create.css",
        "public/css/creative_create.css"
    )
    .postCss(
        "resources/css/creative/index.css",
        "public/css/creative/index.css"
    )
    .postCss(
        "resources/css/advertiser-performance/performance.css",
        "public/css/advertiser-performance/performance.css"
    )
    .postCss(
        "resources/css/advertiser-performance/custom-calendar.css",
        "public/css/advertiser-performance/custom-calendar.css"
    )
    .postCss("resources/css/plan/plan.css", "public/css/plan/plan.css")
    .postCss(
        "resources/css/campaign/campaign.css",
        "public/css/campaign/campaign.css"
    )
    .postCss(
        "resources/css/ga-performance/performance.css",
        "public/css/ga-performance/performance.css"
    )
    .postCss(
        "resources/css/report/ecom/index.css",
        "public/css/report/ecom/index.css"
    );

if (mix.inProduction()) {
    mix.version();
}
